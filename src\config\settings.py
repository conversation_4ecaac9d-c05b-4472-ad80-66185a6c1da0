"""
Configuration management for Arien AI.

This module handles all configuration settings including API keys, model preferences,
and system behavior settings with environment variable support.
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from dotenv import load_dotenv


@dataclass
class LLMConfig:
    """Configuration for LLM providers."""
    provider: str = "deepseek"  # "deepseek" or "ollama"
    model: str = "deepseek-chat"  # deepseek-chat, deepseek-reasoner, or ollama model name
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    max_tokens: int = 4096
    temperature: float = 0.7
    timeout: int = 60


@dataclass
class ToolConfig:
    """Configuration for function tools."""
    bash_enabled: bool = True
    web_search_enabled: bool = True
    bash_timeout: int = 300  # 5 minutes
    bash_max_output_lines: int = 1000
    web_search_max_results: int = 5
    web_search_cache_ttl: int = 3600  # 1 hour


@dataclass
class RetryConfig:
    """Configuration for retry logic."""
    max_retries: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True


@dataclass
class UIConfig:
    """Configuration for user interface."""
    show_progress: bool = True
    show_timestamps: bool = True
    animation_speed: float = 0.2
    confirm_destructive: bool = True
    color_output: bool = True


@dataclass
class Settings:
    """Main configuration class for Arien AI."""
    
    llm: LLMConfig = field(default_factory=LLMConfig)
    tools: ToolConfig = field(default_factory=ToolConfig)
    retry: RetryConfig = field(default_factory=RetryConfig)
    ui: UIConfig = field(default_factory=UIConfig)
    
    # System settings
    log_level: str = "INFO"
    log_file: Optional[str] = None
    config_dir: Path = field(default_factory=lambda: Path.home() / ".arien-ai")
    
    def __post_init__(self) -> None:
        """Initialize settings after creation."""
        self._load_environment()
        self._ensure_config_dir()
    
    def _load_environment(self) -> None:
        """Load settings from environment variables."""
        # Load .env file if it exists
        env_file = Path(".env")
        if env_file.exists():
            load_dotenv(env_file)
        
        # LLM configuration
        self.llm.provider = os.getenv("ARIEN_LLM_PROVIDER", self.llm.provider)
        self.llm.model = os.getenv("ARIEN_LLM_MODEL", self.llm.model)
        self.llm.api_key = os.getenv("DEEPSEEK_API_KEY") or os.getenv("ARIEN_API_KEY")
        self.llm.base_url = os.getenv("ARIEN_BASE_URL", self.llm.base_url)
        
        if os.getenv("ARIEN_MAX_TOKENS"):
            self.llm.max_tokens = int(os.getenv("ARIEN_MAX_TOKENS"))
        if os.getenv("ARIEN_TEMPERATURE"):
            self.llm.temperature = float(os.getenv("ARIEN_TEMPERATURE"))
        if os.getenv("ARIEN_TIMEOUT"):
            self.llm.timeout = int(os.getenv("ARIEN_TIMEOUT"))
        
        # Tool configuration
        if os.getenv("ARIEN_BASH_ENABLED"):
            self.tools.bash_enabled = os.getenv("ARIEN_BASH_ENABLED").lower() == "true"
        if os.getenv("ARIEN_WEB_SEARCH_ENABLED"):
            self.tools.web_search_enabled = os.getenv("ARIEN_WEB_SEARCH_ENABLED").lower() == "true"
        
        # Retry configuration
        if os.getenv("ARIEN_MAX_RETRIES"):
            self.retry.max_retries = int(os.getenv("ARIEN_MAX_RETRIES"))
        
        # UI configuration
        if os.getenv("ARIEN_SHOW_PROGRESS"):
            self.ui.show_progress = os.getenv("ARIEN_SHOW_PROGRESS").lower() == "true"
        if os.getenv("ARIEN_CONFIRM_DESTRUCTIVE"):
            self.ui.confirm_destructive = os.getenv("ARIEN_CONFIRM_DESTRUCTIVE").lower() == "true"
        
        # System settings
        self.log_level = os.getenv("ARIEN_LOG_LEVEL", self.log_level)
        self.log_file = os.getenv("ARIEN_LOG_FILE", self.log_file)
    
    def _ensure_config_dir(self) -> None:
        """Ensure configuration directory exists."""
        self.config_dir.mkdir(parents=True, exist_ok=True)
    
    def get_api_key(self, provider: str) -> Optional[str]:
        """Get API key for specified provider."""
        if provider == "deepseek":
            return self.llm.api_key
        elif provider == "ollama":
            return None  # Ollama doesn't require API key for local models
        return None
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of errors."""
        errors = []
        
        # Validate LLM configuration
        if self.llm.provider == "deepseek" and not self.llm.api_key:
            errors.append("Deepseek API key is required when using Deepseek provider")
        
        if self.llm.provider not in ["deepseek", "ollama"]:
            errors.append(f"Unsupported LLM provider: {self.llm.provider}")
        
        if self.llm.max_tokens <= 0:
            errors.append("max_tokens must be positive")
        
        if not 0 <= self.llm.temperature <= 2:
            errors.append("temperature must be between 0 and 2")
        
        # Validate retry configuration
        if self.retry.max_retries < 0:
            errors.append("max_retries must be non-negative")
        
        if self.retry.base_delay <= 0:
            errors.append("base_delay must be positive")
        
        return errors
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert settings to dictionary."""
        return {
            "llm": {
                "provider": self.llm.provider,
                "model": self.llm.model,
                "max_tokens": self.llm.max_tokens,
                "temperature": self.llm.temperature,
                "timeout": self.llm.timeout,
            },
            "tools": {
                "bash_enabled": self.tools.bash_enabled,
                "web_search_enabled": self.tools.web_search_enabled,
                "bash_timeout": self.tools.bash_timeout,
                "web_search_max_results": self.tools.web_search_max_results,
            },
            "retry": {
                "max_retries": self.retry.max_retries,
                "base_delay": self.retry.base_delay,
                "max_delay": self.retry.max_delay,
            },
            "ui": {
                "show_progress": self.ui.show_progress,
                "show_timestamps": self.ui.show_timestamps,
                "confirm_destructive": self.ui.confirm_destructive,
            },
            "log_level": self.log_level,
        }


# Global settings instance
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """Get global settings instance."""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings


def reset_settings() -> None:
    """Reset global settings instance (mainly for testing)."""
    global _settings
    _settings = None


def apply_config_dict(config_data: Dict[str, Any]) -> None:
    """
    Apply configuration from dictionary to global settings.

    Args:
        config_data: Configuration dictionary
    """
    global _settings
    settings = get_settings()

    # Apply LLM configuration
    if 'llm' in config_data:
        llm_config = config_data['llm']
        if 'provider' in llm_config:
            settings.llm.provider = llm_config['provider']
        if 'model' in llm_config:
            settings.llm.model = llm_config['model']
        if 'api_key' in llm_config:
            settings.llm.api_key = llm_config['api_key']
        if 'base_url' in llm_config:
            settings.llm.base_url = llm_config['base_url']
        if 'max_tokens' in llm_config:
            settings.llm.max_tokens = llm_config['max_tokens']
        if 'temperature' in llm_config:
            settings.llm.temperature = llm_config['temperature']
        if 'timeout' in llm_config:
            settings.llm.timeout = llm_config['timeout']

    # Apply tools configuration
    if 'tools' in config_data:
        tools_config = config_data['tools']
        if 'bash_enabled' in tools_config:
            settings.tools.bash_enabled = tools_config['bash_enabled']
        if 'web_search_enabled' in tools_config:
            settings.tools.web_search_enabled = tools_config['web_search_enabled']
        if 'bash_timeout' in tools_config:
            settings.tools.bash_timeout = tools_config['bash_timeout']
        if 'web_search_max_results' in tools_config:
            settings.tools.web_search_max_results = tools_config['web_search_max_results']

    # Apply retry configuration
    if 'retry' in config_data:
        retry_config = config_data['retry']
        if 'max_retries' in retry_config:
            settings.retry.max_retries = retry_config['max_retries']
        if 'base_delay' in retry_config:
            settings.retry.base_delay = retry_config['base_delay']
        if 'max_delay' in retry_config:
            settings.retry.max_delay = retry_config['max_delay']

    # Apply UI configuration
    if 'ui' in config_data:
        ui_config = config_data['ui']
        if 'show_progress' in ui_config:
            settings.ui.show_progress = ui_config['show_progress']
        if 'show_timestamps' in ui_config:
            settings.ui.show_timestamps = ui_config['show_timestamps']
        if 'confirm_destructive' in ui_config:
            settings.ui.confirm_destructive = ui_config['confirm_destructive']
        if 'color_output' in ui_config:
            settings.ui.color_output = ui_config['color_output']

    # Apply system configuration
    if 'log_level' in config_data:
        settings.log_level = config_data['log_level']
    if 'log_file' in config_data:
        settings.log_file = config_data['log_file']


def apply_profile_settings(profile_settings: Settings) -> None:
    """
    Apply profile settings to global settings.

    Args:
        profile_settings: Settings from profile
    """
    global _settings
    _settings = profile_settings
