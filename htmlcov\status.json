{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.2", "globals": "3c97ea0451b1bd0084e4ad42262d2656", "files": {"z_145eef247bfb46b6___init___py": {"hash": "209f4835f9c6980ec06cc2755b68bbe4", "index": {"url": "z_145eef247bfb46b6___init___py.html", "file": "src\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b695c9c33e1e1997___init___py": {"hash": "0a914627086b8abe443c40eeb4d8792e", "index": {"url": "z_b695c9c33e1e1997___init___py.html", "file": "src\\config\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b695c9c33e1e1997_profiles_py": {"hash": "b837d9e7d9340e9384445d3a99eeef9b", "index": {"url": "z_b695c9c33e1e1997_profiles_py.html", "file": "src\\config\\profiles.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 142, "n_excluded": 0, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b695c9c33e1e1997_settings_py": {"hash": "96b57e4a2788208fda9143265d534b67", "index": {"url": "z_b695c9c33e1e1997_settings_py.html", "file": "src\\config\\settings.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 166, "n_excluded": 0, "n_missing": 55, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ce21df766c911d41___init___py": {"hash": "e57a0850e656558944890b4a04991aa6", "index": {"url": "z_ce21df766c911d41___init___py.html", "file": "src\\core\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ce21df766c911d41_agent_py": {"hash": "4d3a3cab44e121568ec01aed66ae9871", "index": {"url": "z_ce21df766c911d41_agent_py.html", "file": "src\\core\\agent.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 157, "n_excluded": 0, "n_missing": 118, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ce21df766c911d41_context_py": {"hash": "2ab3624820035dff84f67e0114d66269", "index": {"url": "z_ce21df766c911d41_context_py.html", "file": "src\\core\\context.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 144, "n_excluded": 0, "n_missing": 54, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ce21df766c911d41_exceptions_py": {"hash": "026b258c7f9c9d23b04d17ffcb920e22", "index": {"url": "z_ce21df766c911d41_exceptions_py.html", "file": "src\\core\\exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 62, "n_excluded": 0, "n_missing": 12, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ce21df766c911d41_logging_py": {"hash": "8d1e5f078a50c1aee3532b24c4c5fe0e", "index": {"url": "z_ce21df766c911d41_logging_py.html", "file": "src\\core\\logging.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 92, "n_excluded": 0, "n_missing": 92, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ce21df766c911d41_retry_py": {"hash": "f5254cc3d79e92f48f2541d01e6ccd24", "index": {"url": "z_ce21df766c911d41_retry_py.html", "file": "src\\core\\retry.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 84, "n_excluded": 0, "n_missing": 30, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_main_py": {"hash": "dec106c337f323a69c280ce592dc7b6e", "index": {"url": "z_145eef247bfb46b6_main_py.html", "file": "src\\main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 217, "n_excluded": 0, "n_missing": 217, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7b455957374e7e60___init___py": {"hash": "5d3c52c08c0d165c66aceb98dc22be23", "index": {"url": "z_7b455957374e7e60___init___py.html", "file": "src\\providers\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7b455957374e7e60_base_py": {"hash": "9cb49c4918e59277be81fd43be26bdbb", "index": {"url": "z_7b455957374e7e60_base_py.html", "file": "src\\providers\\base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 78, "n_excluded": 0, "n_missing": 30, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7b455957374e7e60_deepseek_py": {"hash": "1517ab19a5e155dc632b0d07cec00430", "index": {"url": "z_7b455957374e7e60_deepseek_py.html", "file": "src\\providers\\deepseek.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 105, "n_excluded": 0, "n_missing": 82, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7b455957374e7e60_ollama_py": {"hash": "ef9243e65a99c64c17d50fbb20523495", "index": {"url": "z_7b455957374e7e60_ollama_py.html", "file": "src\\providers\\ollama.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 115, "n_excluded": 0, "n_missing": 91, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bef636d57f78fa40___init___py": {"hash": "5792f02a97568ab056284ed199cbc14e", "index": {"url": "z_bef636d57f78fa40___init___py.html", "file": "src\\tools\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bef636d57f78fa40_base_py": {"hash": "601e59fa048f3308601cd39a452ecffc", "index": {"url": "z_bef636d57f78fa40_base_py.html", "file": "src\\tools\\base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 71, "n_excluded": 0, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bef636d57f78fa40_bash_tool_py": {"hash": "cc7d70230f52d45ccb4781499fe85f1d", "index": {"url": "z_bef636d57f78fa40_bash_tool_py.html", "file": "src\\tools\\bash_tool.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 98, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bef636d57f78fa40_file_tool_py": {"hash": "cb3b0d6def2ce3e149c6f19806ddc245", "index": {"url": "z_bef636d57f78fa40_file_tool_py.html", "file": "src\\tools\\file_tool.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 260, "n_excluded": 0, "n_missing": 106, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bef636d57f78fa40_web_search_tool_py": {"hash": "de32fef3097eecde6c15cdb947f91c12", "index": {"url": "z_bef636d57f78fa40_web_search_tool_py.html", "file": "src\\tools\\web_search_tool.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 136, "n_excluded": 0, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d8dae034b85f0cf9___init___py": {"hash": "640ca9ea185f1ca4fd39a0bfb514dbd5", "index": {"url": "z_d8dae034b85f0cf9___init___py.html", "file": "src\\ui\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d8dae034b85f0cf9_animation_py": {"hash": "7f077727caf203419add639456c53c30", "index": {"url": "z_d8dae034b85f0cf9_animation_py.html", "file": "src\\ui\\animation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 115, "n_excluded": 0, "n_missing": 115, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d8dae034b85f0cf9_cli_py": {"hash": "6cf9285992567681aaff308c6b1f7baa", "index": {"url": "z_d8dae034b85f0cf9_cli_py.html", "file": "src\\ui\\cli.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 145, "n_excluded": 0, "n_missing": 145, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d8dae034b85f0cf9_formatter_py": {"hash": "ced52c29dbd3eedcb21d7efc2b0eaea0", "index": {"url": "z_d8dae034b85f0cf9_formatter_py.html", "file": "src\\ui\\formatter.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 133, "n_excluded": 0, "n_missing": 133, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}