"""
Base classes for LLM providers.

This module defines the interface that all LLM providers must implement,
ensuring consistent behavior across different providers.
"""

import json
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List, Dict, Any, Optional, AsyncIterator, Union
from enum import Enum


class MessageRole(Enum):
    """Message roles in conversation."""
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"
    TOOL = "tool"


@dataclass
class Message:
    """Represents a message in the conversation."""
    role: MessageRole
    content: str
    tool_calls: Optional[List[Dict[str, Any]]] = None
    tool_call_id: Optional[str] = None
    name: Optional[str] = None


@dataclass
class ToolCall:
    """Represents a tool call from the LLM."""
    id: str
    name: str
    arguments: Dict[str, Any]


@dataclass
class LLMResponse:
    """Response from LLM provider."""
    content: str
    tool_calls: List[ToolCall]
    finish_reason: str
    usage: Optional[Dict[str, int]] = None
    model: Optional[str] = None


class BaseLLMProvider(ABC):
    """
    Abstract base class for all LLM providers.
    
    This class defines the interface that all LLM providers must implement
    to ensure consistent behavior across different providers.
    """
    
    def __init__(self, **kwargs) -> None:
        """Initialize the provider with configuration."""
        pass
    
    @abstractmethod
    async def generate_response(
        self,
        messages: List[Message],
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> LLMResponse:
        """
        Generate a response from the LLM.
        
        Args:
            messages: List of conversation messages
            tools: Optional list of available tools
            **kwargs: Additional provider-specific parameters
            
        Returns:
            LLM response
        """
        pass
    
    @abstractmethod
    async def stream_response(
        self,
        messages: List[Message],
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> AsyncIterator[str]:
        """
        Stream response from the LLM.
        
        Args:
            messages: List of conversation messages
            tools: Optional list of available tools
            **kwargs: Additional provider-specific parameters
            
        Yields:
            Response chunks as they arrive
        """
        pass
    
    @abstractmethod
    async def validate_connection(self) -> bool:
        """
        Validate connection to the LLM provider.
        
        Returns:
            True if connection is valid, False otherwise
        """
        pass
    
    def format_messages(self, messages: List[Message]) -> List[Dict[str, Any]]:
        """
        Format messages for the provider's API.
        
        Args:
            messages: List of messages to format
            
        Returns:
            Formatted messages for API
        """
        formatted = []
        for msg in messages:
            formatted_msg = {
                "role": msg.role.value,
                "content": msg.content,
            }
            
            if msg.tool_calls:
                formatted_msg["tool_calls"] = msg.tool_calls
            
            if msg.tool_call_id:
                formatted_msg["tool_call_id"] = msg.tool_call_id
            
            if msg.name:
                formatted_msg["name"] = msg.name
            
            formatted.append(formatted_msg)
        
        return formatted
    
    def parse_tool_calls(self, tool_calls_data: List[Dict[str, Any]]) -> List[ToolCall]:
        """
        Parse tool calls from API response.
        
        Args:
            tool_calls_data: Raw tool calls data from API
            
        Returns:
            List of parsed tool calls
        """
        tool_calls = []
        for call_data in tool_calls_data:
            try:
                # Handle different API formats
                if "function" in call_data:
                    # OpenAI-style format
                    func_data = call_data["function"]
                    arguments = json.loads(func_data["arguments"]) if isinstance(func_data["arguments"], str) else func_data["arguments"]
                    tool_calls.append(ToolCall(
                        id=call_data["id"],
                        name=func_data["name"],
                        arguments=arguments
                    ))
                else:
                    # Direct format
                    arguments = json.loads(call_data["arguments"]) if isinstance(call_data["arguments"], str) else call_data["arguments"]
                    tool_calls.append(ToolCall(
                        id=call_data["id"],
                        name=call_data["name"],
                        arguments=arguments
                    ))
            except (json.JSONDecodeError, KeyError) as e:
                # Log error but continue processing other tool calls
                continue
        
        return tool_calls
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Provider name."""
        pass
    
    @property
    @abstractmethod
    def supports_streaming(self) -> bool:
        """Whether provider supports streaming."""
        pass
    
    @property
    @abstractmethod
    def supports_tools(self) -> bool:
        """Whether provider supports function calling."""
        pass
