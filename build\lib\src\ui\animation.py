"""
Animation components for the CLI interface.

This module provides the ball animation with elapsed time display
as specified in the requirements.
"""

import asyncio
import time
from typing import Optional


class BallAnimation:
    """
    Ball animation with elapsed time display.
    
    Shows a bouncing ball animation with elapsed time counter
    to indicate that the system is working on a task.
    """
    
    # Animation frames as specified in requirements
    FRAMES = [
        "( ●    )",
        "(  ●   )",
        "(   ●  )",
        "(    ● )",
        "(     ●)",
        "(    ● )",
        "(   ●  )",
        "(  ●   )",
        "( ●    )",
        "(●     )"
    ]
    
    def __init__(self, message: str = "Processing", speed: float = 0.2) -> None:
        """
        Initialize ball animation.
        
        Args:
            message: Message to display with animation
            speed: Animation speed in seconds between frames
        """
        self.message = message
        self.speed = speed
        self.is_running = False
        self.start_time: Optional[float] = None
        self._task: Optional[asyncio.Task] = None
    
    def _format_elapsed_time(self, elapsed: float) -> str:
        """Format elapsed time as MM:SS."""
        minutes = int(elapsed // 60)
        seconds = int(elapsed % 60)
        return f"{minutes:02d}:{seconds:02d}"
    
    async def _animate(self) -> None:
        """Run the animation loop."""
        frame_index = 0
        
        while self.is_running:
            # Calculate elapsed time
            elapsed = time.time() - self.start_time if self.start_time else 0
            elapsed_str = self._format_elapsed_time(elapsed)
            
            # Get current frame
            frame = self.FRAMES[frame_index]
            
            # Display animation with elapsed time
            print(f"\r{frame} {self.message}... [{elapsed_str}]", end="", flush=True)
            
            # Move to next frame
            frame_index = (frame_index + 1) % len(self.FRAMES)
            
            # Wait for next frame
            await asyncio.sleep(self.speed)
    
    def start(self) -> None:
        """Start the animation."""
        if not self.is_running:
            self.is_running = True
            self.start_time = time.time()
            self._task = asyncio.create_task(self._animate())
    
    async def stop(self) -> None:
        """Stop the animation."""
        if self.is_running:
            self.is_running = False
            
            if self._task:
                self._task.cancel()
                try:
                    await self._task
                except asyncio.CancelledError:
                    pass
                self._task = None
            
            # Clear the animation line
            print("\r" + " " * 50 + "\r", end="", flush=True)
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.stop()


class ProgressIndicator:
    """
    Simple progress indicator for tasks with known progress.
    """
    
    def __init__(self, total: int, message: str = "Progress", width: int = 40) -> None:
        """
        Initialize progress indicator.
        
        Args:
            total: Total number of items to process
            message: Message to display
            width: Width of progress bar in characters
        """
        self.total = total
        self.message = message
        self.width = width
        self.current = 0
        self.start_time = time.time()
    
    def update(self, current: int) -> None:
        """
        Update progress.
        
        Args:
            current: Current progress value
        """
        self.current = min(current, self.total)
        self._display()
    
    def increment(self) -> None:
        """Increment progress by 1."""
        self.update(self.current + 1)
    
    def _display(self) -> None:
        """Display current progress."""
        if self.total == 0:
            percentage = 100
            filled_width = self.width
        else:
            percentage = (self.current / self.total) * 100
            filled_width = int((self.current / self.total) * self.width)
        
        # Create progress bar
        bar = "█" * filled_width + "░" * (self.width - filled_width)
        
        # Calculate elapsed time and ETA
        elapsed = time.time() - self.start_time
        if self.current > 0 and self.current < self.total:
            eta = (elapsed / self.current) * (self.total - self.current)
            eta_str = f" ETA: {self._format_time(eta)}"
        else:
            eta_str = ""
        
        # Display progress
        print(
            f"\r{self.message}: [{bar}] {percentage:5.1f}% "
            f"({self.current}/{self.total}){eta_str}",
            end="",
            flush=True
        )
    
    def _format_time(self, seconds: float) -> str:
        """Format time as HH:MM:SS or MM:SS."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"
        else:
            return f"{minutes:02d}:{secs:02d}"
    
    def finish(self) -> None:
        """Mark progress as complete."""
        self.update(self.total)
        elapsed = time.time() - self.start_time
        print(f" ✓ Completed in {self._format_time(elapsed)}")


class Spinner:
    """
    Simple spinner animation for indefinite tasks.
    """
    
    FRAMES = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]
    
    def __init__(self, message: str = "Loading", speed: float = 0.1) -> None:
        """
        Initialize spinner.
        
        Args:
            message: Message to display with spinner
            speed: Animation speed in seconds between frames
        """
        self.message = message
        self.speed = speed
        self.is_running = False
        self._task: Optional[asyncio.Task] = None
    
    async def _animate(self) -> None:
        """Run the spinner animation."""
        frame_index = 0
        
        while self.is_running:
            frame = self.FRAMES[frame_index]
            print(f"\r{frame} {self.message}...", end="", flush=True)
            
            frame_index = (frame_index + 1) % len(self.FRAMES)
            await asyncio.sleep(self.speed)
    
    def start(self) -> None:
        """Start the spinner."""
        if not self.is_running:
            self.is_running = True
            self._task = asyncio.create_task(self._animate())
    
    async def stop(self) -> None:
        """Stop the spinner."""
        if self.is_running:
            self.is_running = False
            
            if self._task:
                self._task.cancel()
                try:
                    await self._task
                except asyncio.CancelledError:
                    pass
                self._task = None
            
            # Clear the spinner line
            print("\r" + " " * (len(self.message) + 10) + "\r", end="", flush=True)
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.stop()
