"""
Interactive dropdown component for tool confirmations.
"""

import sys
from typing import List, Optional, Any
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.align import Align
from rich.table import Table
import keyboard
import time


class DropdownChoice:
    """Represents a choice in the dropdown."""
    
    def __init__(self, value: str, label: str, description: Optional[str] = None, style: str = ""):
        self.value = value
        self.label = label
        self.description = description
        self.style = style


class InteractiveDropdown:
    """Interactive dropdown component using Rich and keyboard."""
    
    def __init__(self, console: Optional[Console] = None):
        self.console = console or Console()
        self.selected_index = 0
        self.choices: List[DropdownChoice] = []
        
    def add_choice(self, value: str, label: str, description: Optional[str] = None, style: str = "") -> None:
        """Add a choice to the dropdown."""
        self.choices.append(DropdownChoice(value, label, description, style))
    
    def _render_dropdown(self, title: str, message: str) -> None:
        """Render the dropdown interface."""
        self.console.clear()
        
        # Create title
        title_text = Text(title, style="bold blue")
        title_panel = Panel(Align.center(title_text), style="blue")
        self.console.print(title_panel)
        
        # Create message
        if message:
            message_text = Text(message, style="white")
            message_panel = Panel(message_text, style="white")
            self.console.print(message_panel)
        
        # Create choices table
        table = Table(show_header=False, show_lines=False, padding=(0, 2))
        table.add_column("Indicator", width=3)
        table.add_column("Choice", min_width=20)
        table.add_column("Description", min_width=30)
        
        for i, choice in enumerate(self.choices):
            indicator = "►" if i == self.selected_index else " "
            indicator_style = "bold green" if i == self.selected_index else "dim"
            
            choice_style = choice.style if choice.style else ("bold white" if i == self.selected_index else "white")
            desc_style = "dim white" if i == self.selected_index else "dim"
            
            table.add_row(
                Text(indicator, style=indicator_style),
                Text(choice.label, style=choice_style),
                Text(choice.description or "", style=desc_style)
            )
        
        choices_panel = Panel(table, title="Options", style="green")
        self.console.print(choices_panel)
        
        # Instructions
        instructions = Text("Use ↑/↓ arrows to navigate, Enter to select, Esc to cancel", style="dim")
        self.console.print(Align.center(instructions))
    
    def show(self, title: str, message: str = "") -> Optional[str]:
        """
        Show the dropdown and return the selected value.
        
        Args:
            title: Title of the dropdown
            message: Optional message to display
            
        Returns:
            Selected value or None if cancelled
        """
        if not self.choices:
            return None
        
        # Always use fallback for now since keyboard input is complex in different environments
        return self._fallback_input(title, message)
    
    def _fallback_input(self, title: str, message: str) -> Optional[str]:
        """Fallback to simple text input when interactive mode fails."""
        self.console.print(f"\n[bold blue]{title}[/bold blue]")
        if message:
            self.console.print(f"{message}")
        
        self.console.print("\nOptions:")
        for i, choice in enumerate(self.choices):
            self.console.print(f"  {i + 1}. {choice.label}")
            if choice.description:
                self.console.print(f"     {choice.description}")
        
        try:
            while True:
                response = input(f"\nSelect option (1-{len(self.choices)}) or 'q' to cancel: ").strip().lower()
                
                if response == 'q':
                    return None
                
                try:
                    index = int(response) - 1
                    if 0 <= index < len(self.choices):
                        return self.choices[index].value
                    else:
                        print(f"Please enter a number between 1 and {len(self.choices)}")
                except ValueError:
                    print("Please enter a valid number or 'q' to cancel")
                    
        except (KeyboardInterrupt, EOFError):
            return None


def create_tool_confirmation_dropdown(tool_name: str, tool_args: dict) -> Optional[str]:
    """
    Create a dropdown for tool execution confirmation.
    
    Args:
        tool_name: Name of the tool
        tool_args: Tool arguments
        
    Returns:
        'yes', 'no', 'details', or None if cancelled
    """
    dropdown = InteractiveDropdown()
    
    # Add choices
    dropdown.add_choice(
        "yes", 
        "✅ Execute", 
        "Proceed with tool execution",
        "bold green"
    )
    dropdown.add_choice(
        "no", 
        "❌ Cancel", 
        "Cancel the operation",
        "bold red"
    )
    dropdown.add_choice(
        "details", 
        "📋 Show Details", 
        "View detailed information about this operation",
        "bold yellow"
    )
    
    # Format arguments for display
    args_str = ", ".join([f"{k}={v}" for k, v in tool_args.items()])
    message = f"Tool '{tool_name}' wants to execute with arguments:\n{args_str}"
    
    return dropdown.show("🔧 Tool Execution Confirmation", message)


def create_simple_confirmation_dropdown(message: str, title: str = "Confirmation") -> Optional[str]:
    """
    Create a simple yes/no confirmation dropdown.
    
    Args:
        message: Confirmation message
        title: Title of the dropdown
        
    Returns:
        'yes' or 'no', or None if cancelled
    """
    dropdown = InteractiveDropdown()
    
    dropdown.add_choice("yes", "✅ Yes", "Confirm the action", "bold green")
    dropdown.add_choice("no", "❌ No", "Cancel the action", "bold red")
    
    return dropdown.show(title, message)
