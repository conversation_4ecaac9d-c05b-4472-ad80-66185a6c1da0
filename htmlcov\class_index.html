<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">43%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-08 17:43 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src\__init__.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997___init___py.html">src\config\__init__.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t15">src\config\profiles.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t15"><data value='ConfigProfile'>ConfigProfile</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t23">src\config\profiles.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t23"><data value='ProfileManager'>ProfileManager</data></a></td>
                <td>120</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="95 120">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html">src\config\profiles.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="21 22">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t16">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t16"><data value='LLMConfig'>LLMConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t28">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t28"><data value='ToolConfig'>ToolConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t39">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t39"><data value='RetryConfig'>RetryConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t49">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t49"><data value='UIConfig'>UIConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t59">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t59"><data value='Settings'>Settings</data></a></td>
                <td>56</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="51 56">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>110</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="60 110">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41___init___py.html">src\core\__init__.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t29">src\core\agent.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t29"><data value='AgentResponse'>AgentResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t37">src\core\agent.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t37"><data value='ToolExecution'>ToolExecution</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t44">src\core\agent.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t44"><data value='ArienAgent'>ArienAgent</data></a></td>
                <td>118</td>
                <td>118</td>
                <td>0</td>
                <td class="right" data-ratio="0 118">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html">src\core\agent.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 39">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t21">src\core\context.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t21"><data value='ConversationTurn'>ConversationTurn</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t32">src\core\context.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t32"><data value='SessionInfo'>SessionInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t42">src\core\context.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t42"><data value='ConversationContext'>ConversationContext</data></a></td>
                <td>102</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="48 102">47%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html">src\core\context.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>42</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="42 42">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t11">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t11"><data value='ArienError'>ArienError</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t31">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t31"><data value='ConfigurationError'>ConfigurationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t36">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t36"><data value='LLMError'>LLMError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t41">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t41"><data value='ToolError'>ToolError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t46">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t46"><data value='RetryableError'>RetryableError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t56">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t56"><data value='NonRetryableError'>NonRetryableError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t67">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t67"><data value='LLMConnectionError'>LLMConnectionError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t72">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t72"><data value='LLMRateLimitError'>LLMRateLimitError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t77">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t77"><data value='LLMAuthenticationError'>LLMAuthenticationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t82">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t82"><data value='LLMInvalidRequestError'>LLMInvalidRequestError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t87">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t87"><data value='LLMTimeoutError'>LLMTimeoutError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t93">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t93"><data value='BashExecutionError'>BashExecutionError</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t112">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t112"><data value='BashTimeoutError'>BashTimeoutError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t117">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t117"><data value='WebSearchError'>WebSearchError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t122">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t122"><data value='WebSearchRateLimitError'>WebSearchRateLimitError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t127">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t127"><data value='WebSearchConnectionError'>WebSearchConnectionError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t132">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t132"><data value='WebSearchInvalidQueryError'>WebSearchInvalidQueryError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>50</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="39 50">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t21">src\core\logging.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t21"><data value='StructuredFormatter'>StructuredFormatter</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t45">src\core\logging.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t45"><data value='PerformanceLogger'>PerformanceLogger</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html">src\core\logging.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>67</td>
                <td>67</td>
                <td>0</td>
                <td class="right" data-ratio="0 67">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_retry_py.html#t23">src\core\retry.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_retry_py.html#t23"><data value='RetryManager'>RetryManager</data></a></td>
                <td>56</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="30 56">54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_retry_py.html">src\core\retry.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_retry_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="24 28">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>217</td>
                <td>217</td>
                <td>0</td>
                <td class="right" data-ratio="0 217">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60___init___py.html">src\providers\__init__.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t15">src\providers\base.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t15"><data value='MessageRole'>MessageRole</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t24">src\providers\base.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t24"><data value='Message'>Message</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t34">src\providers\base.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t34"><data value='ToolCall'>ToolCall</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t42">src\providers\base.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t42"><data value='LLMResponse'>LLMResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t51">src\providers\base.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t51"><data value='BaseLLMProvider'>BaseLLMProvider</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html">src\providers\base.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>48</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="48 48">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html#t28">src\providers\deepseek.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html#t28"><data value='DeepseekProvider'>DeepseekProvider</data></a></td>
                <td>82</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="0 82">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html">src\providers\deepseek.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t25">src\providers\ollama.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t25"><data value='OllamaProvider'>OllamaProvider</data></a></td>
                <td>91</td>
                <td>91</td>
                <td>0</td>
                <td class="right" data-ratio="0 91">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html">src\providers\ollama.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40___init___py.html">src\tools\__init__.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t15">src\tools\base.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t15"><data value='ToolStatus'>ToolStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t24">src\tools\base.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t24"><data value='ToolResult'>ToolResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t33">src\tools\base.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t33"><data value='BaseTool'>BaseTool</data></a></td>
                <td>32</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="18 32">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html">src\tools\base.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 39">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html#t22">src\tools\bash_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html#t22"><data value='BashTool'>BashTool</data></a></td>
                <td>70</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="63 70">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html">src\tools\bash_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t22">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t22"><data value='FileTool'>FileTool</data></a></td>
                <td>220</td>
                <td>106</td>
                <td>0</td>
                <td class="right" data-ratio="114 220">52%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="40 40">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t31">src\tools\web_search_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t31"><data value='WebSearchTool'>WebSearchTool</data></a></td>
                <td>101</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="87 101">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html">src\tools\web_search_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9___init___py.html">src\ui\__init__.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t13">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t13"><data value='BallAnimation'>BallAnimation</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t109">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t109"><data value='ProgressIndicator'>ProgressIndicator</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t189">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t189"><data value='Spinner'>Spinner</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t26">src\ui\cli.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t26"><data value='CLI'>CLI</data></a></td>
                <td>118</td>
                <td>118</td>
                <td>0</td>
                <td class="right" data-ratio="0 118">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html">src\ui\cli.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t23">src\ui\formatter.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t23"><data value='OutputFormatter'>OutputFormatter</data></a></td>
                <td>104</td>
                <td>104</td>
                <td>0</td>
                <td class="right" data-ratio="0 104">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html">src\ui\formatter.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>2343</td>
                <td>1345</td>
                <td>0</td>
                <td class="right" data-ratio="998 2343">43%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-08 17:43 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
