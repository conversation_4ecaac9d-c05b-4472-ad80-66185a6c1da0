"""
Intelligent retry logic with exponential backoff.

This module implements a sophisticated retry system that can distinguish between
retryable and non-retryable errors, with configurable backoff strategies.
"""

import asyncio
import logging
import random
import time
from typing import TypeVar, Callable, Any, Optional, Union, Awaitable
from functools import wraps

from src.core.exceptions import is_retryable_error, RetryableError
from src.config.settings import get_settings

logger = logging.getLogger(__name__)

T = TypeVar('T')


class RetryManager:
    """
    Manages retry logic with exponential backoff and jitter.
    
    Features:
    - Exponential backoff with configurable base and maximum delays
    - Optional jitter to prevent thundering herd
    - Intelligent error classification
    - Detailed logging of retry attempts
    - Support for both sync and async functions
    """
    
    def __init__(
        self,
        max_retries: Optional[int] = None,
        base_delay: Optional[float] = None,
        max_delay: Optional[float] = None,
        exponential_base: Optional[float] = None,
        jitter: Optional[bool] = None,
    ) -> None:
        """
        Initialize retry manager.
        
        Args:
            max_retries: Maximum number of retry attempts
            base_delay: Base delay in seconds
            max_delay: Maximum delay in seconds
            exponential_base: Base for exponential backoff
            jitter: Whether to add random jitter
        """
        settings = get_settings()
        
        self.max_retries = max_retries or settings.retry.max_retries
        self.base_delay = base_delay or settings.retry.base_delay
        self.max_delay = max_delay or settings.retry.max_delay
        self.exponential_base = exponential_base or settings.retry.exponential_base
        self.jitter = jitter if jitter is not None else settings.retry.jitter
    
    def calculate_delay(self, attempt: int) -> float:
        """
        Calculate delay for given attempt number.
        
        Args:
            attempt: Current attempt number (0-based)
            
        Returns:
            Delay in seconds
        """
        # Exponential backoff
        delay = self.base_delay * (self.exponential_base ** attempt)
        
        # Cap at maximum delay
        delay = min(delay, self.max_delay)
        
        # Add jitter if enabled
        if self.jitter:
            # Add up to 25% random jitter
            jitter_amount = delay * 0.25 * random.random()
            delay += jitter_amount
        
        return delay
    
    async def retry_async(
        self,
        func: Callable[..., Awaitable[T]],
        *args,
        **kwargs
    ) -> T:
        """
        Retry an async function with exponential backoff.
        
        Args:
            func: Async function to retry
            *args: Positional arguments for function
            **kwargs: Keyword arguments for function
            
        Returns:
            Function result
            
        Raises:
            Last exception if all retries exhausted
        """
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                logger.debug(f"Attempt {attempt + 1}/{self.max_retries + 1} for {func.__name__}")
                result = await func(*args, **kwargs)
                
                if attempt > 0:
                    logger.info(f"Function {func.__name__} succeeded after {attempt + 1} attempts")
                
                return result
                
            except Exception as e:
                last_exception = e
                
                # Check if error is retryable
                if not is_retryable_error(e):
                    logger.debug(f"Non-retryable error in {func.__name__}: {e}")
                    raise e
                
                # Don't retry if this was the last attempt
                if attempt >= self.max_retries:
                    logger.error(f"All retry attempts exhausted for {func.__name__}")
                    break
                
                # Calculate delay and wait
                delay = self.calculate_delay(attempt)
                logger.warning(
                    f"Attempt {attempt + 1} failed for {func.__name__}: {e}. "
                    f"Retrying in {delay:.2f} seconds..."
                )
                
                await asyncio.sleep(delay)
        
        # All retries exhausted
        if last_exception:
            raise last_exception
        else:
            raise RuntimeError("Unexpected error: no exception recorded")
    
    def retry_sync(
        self,
        func: Callable[..., T],
        *args,
        **kwargs
    ) -> T:
        """
        Retry a sync function with exponential backoff.
        
        Args:
            func: Function to retry
            *args: Positional arguments for function
            **kwargs: Keyword arguments for function
            
        Returns:
            Function result
            
        Raises:
            Last exception if all retries exhausted
        """
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                logger.debug(f"Attempt {attempt + 1}/{self.max_retries + 1} for {func.__name__}")
                result = func(*args, **kwargs)
                
                if attempt > 0:
                    logger.info(f"Function {func.__name__} succeeded after {attempt + 1} attempts")
                
                return result
                
            except Exception as e:
                last_exception = e
                
                # Check if error is retryable
                if not is_retryable_error(e):
                    logger.debug(f"Non-retryable error in {func.__name__}: {e}")
                    raise e
                
                # Don't retry if this was the last attempt
                if attempt >= self.max_retries:
                    logger.error(f"All retry attempts exhausted for {func.__name__}")
                    break
                
                # Calculate delay and wait
                delay = self.calculate_delay(attempt)
                logger.warning(
                    f"Attempt {attempt + 1} failed for {func.__name__}: {e}. "
                    f"Retrying in {delay:.2f} seconds..."
                )
                
                time.sleep(delay)
        
        # All retries exhausted
        if last_exception:
            raise last_exception
        else:
            raise RuntimeError("Unexpected error: no exception recorded")


def retry_on_failure(
    max_retries: Optional[int] = None,
    base_delay: Optional[float] = None,
    max_delay: Optional[float] = None,
    exponential_base: Optional[float] = None,
    jitter: Optional[bool] = None,
):
    """
    Decorator for automatic retry with exponential backoff.
    
    Args:
        max_retries: Maximum number of retry attempts
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds
        exponential_base: Base for exponential backoff
        jitter: Whether to add random jitter
    """
    def decorator(func: Callable) -> Callable:
        retry_manager = RetryManager(
            max_retries=max_retries,
            base_delay=base_delay,
            max_delay=max_delay,
            exponential_base=exponential_base,
            jitter=jitter,
        )
        
        if asyncio.iscoroutinefunction(func):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                return await retry_manager.retry_async(func, *args, **kwargs)
            return async_wrapper
        else:
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                return retry_manager.retry_sync(func, *args, **kwargs)
            return sync_wrapper
    
    return decorator
