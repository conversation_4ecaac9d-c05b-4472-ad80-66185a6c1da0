"""
Base classes for function tools.

This module defines the interface that all tools must implement,
ensuring consistent behavior and enabling intelligent execution strategies.
"""

import asyncio
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, Any, Optional, List
from enum import Enum


class ToolStatus(Enum):
    """Status of tool execution."""
    SUCCESS = "success"
    ERROR = "error"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


@dataclass
class ToolResult:
    """Result of tool execution."""
    status: ToolStatus
    output: str
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    execution_time: Optional[float] = None


class BaseTool(ABC):
    """
    Abstract base class for all function tools.
    
    This class defines the interface that all tools must implement
    to ensure consistent behavior and enable intelligent orchestration.
    """
    
    def __init__(self, **kwargs: Any) -> None:
        """Initialize the tool with configuration."""
        pass
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Tool name."""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Tool description for LLM."""
        pass
    
    @property
    @abstractmethod
    def parameters(self) -> Dict[str, Any]:
        """Tool parameters schema (JSON Schema format)."""
        pass
    
    @abstractmethod
    async def execute(self, **kwargs: Any) -> ToolResult:
        """
        Execute the tool with given parameters.
        
        Args:
            **kwargs: Tool parameters
            
        Returns:
            Tool execution result
        """
        pass
    
    @property
    def can_run_parallel(self) -> bool:
        """
        Whether this tool can be run in parallel with other tools.
        
        Returns:
            True if tool can run in parallel, False if it must run sequentially
        """
        return True
    
    @property
    def is_destructive(self) -> bool:
        """
        Whether this tool performs destructive operations.
        
        Returns:
            True if tool is destructive and requires confirmation
        """
        return False
    
    @property
    def requires_confirmation(self) -> bool:
        """
        Whether this tool requires user confirmation before execution.
        
        Returns:
            True if confirmation is required
        """
        return self.is_destructive
    
    def get_schema(self) -> Dict[str, Any]:
        """
        Get tool schema for LLM function calling.
        
        Returns:
            Tool schema in OpenAI function calling format
        """
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": self.parameters,
            }
        }
    
    def validate_parameters(self, **kwargs: Any) -> List[str]:
        """
        Validate tool parameters.
        
        Args:
            **kwargs: Parameters to validate
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        # Check required parameters
        required = self.parameters.get("required", [])
        for param in required:
            if param not in kwargs:
                errors.append(f"Missing required parameter: {param}")
        
        # Check parameter types (basic validation)
        properties = self.parameters.get("properties", {})
        for param, value in kwargs.items():
            if param in properties:
                expected_type = properties[param].get("type")
                if expected_type == "string" and not isinstance(value, str):
                    errors.append(f"Parameter {param} must be a string")
                elif expected_type == "integer" and not isinstance(value, int):
                    errors.append(f"Parameter {param} must be an integer")
                elif expected_type == "number" and not isinstance(value, (int, float)):
                    errors.append(f"Parameter {param} must be a number")
                elif expected_type == "boolean" and not isinstance(value, bool):
                    errors.append(f"Parameter {param} must be a boolean")
                elif expected_type == "array" and not isinstance(value, list):
                    errors.append(f"Parameter {param} must be an array")
                elif expected_type == "object" and not isinstance(value, dict):
                    errors.append(f"Parameter {param} must be an object")
        
        return errors
    
    async def __call__(self, **kwargs: Any) -> ToolResult:
        """Make tool callable."""
        return await self.execute(**kwargs)
