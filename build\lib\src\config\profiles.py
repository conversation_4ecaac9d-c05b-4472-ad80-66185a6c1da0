"""
Configuration profiles for different use cases.

This module provides predefined configuration profiles for common scenarios
like development, production, research, and system administration.
"""

from dataclasses import dataclass, replace
from typing import Dict, Any, Optional

from src.config.settings import Settings, LLMConfig, ToolConfig, RetryConfig, UIConfig


@dataclass
class ConfigProfile:
    """Configuration profile with metadata."""
    name: str
    description: str
    settings: Settings
    tags: list[str]


class ProfileManager:
    """Manages configuration profiles for different use cases."""
    
    def __init__(self):
        self._profiles: Dict[str, ConfigProfile] = {}
        self._load_builtin_profiles()
    
    def _load_builtin_profiles(self) -> None:
        """Load built-in configuration profiles."""
        
        # Development Profile
        dev_settings = Settings()
        dev_settings.llm.temperature = 0.3  # More deterministic
        dev_settings.tools.bash_timeout = 60  # Shorter timeout for dev
        dev_settings.ui.confirm_destructive = False  # Less confirmation for dev
        dev_settings.log_level = "DEBUG"
        dev_settings.retry.max_retries = 1  # Fail fast in development
        
        self._profiles["development"] = ConfigProfile(
            name="development",
            description="Optimized for software development with faster execution and detailed logging",
            settings=dev_settings,
            tags=["dev", "coding", "fast"]
        )
        
        # Production Profile
        prod_settings = Settings()
        prod_settings.llm.temperature = 0.7  # Balanced creativity
        prod_settings.tools.bash_timeout = 300  # Standard timeout
        prod_settings.ui.confirm_destructive = True  # Always confirm in production
        prod_settings.log_level = "INFO"
        prod_settings.retry.max_retries = 5  # More retries for reliability
        
        self._profiles["production"] = ConfigProfile(
            name="production",
            description="Stable configuration for production environments with enhanced safety",
            settings=prod_settings,
            tags=["prod", "stable", "safe"]
        )
        
        # Research Profile
        research_settings = Settings()
        research_settings.llm.temperature = 0.8  # More creative responses
        research_settings.llm.max_tokens = 8192  # Longer responses
        research_settings.tools.web_search_enabled = True  # Essential for research
        research_settings.tools.web_search_max_results = 10  # More search results
        research_settings.ui.show_timestamps = True  # Track research sessions
        
        self._profiles["research"] = ConfigProfile(
            name="research",
            description="Enhanced for research tasks with web search and detailed analysis",
            settings=research_settings,
            tags=["research", "analysis", "web"]
        )
        
        # System Administration Profile
        sysadmin_settings = Settings()
        sysadmin_settings.llm.temperature = 0.2  # Very deterministic
        sysadmin_settings.tools.bash_enabled = True  # Essential for sysadmin
        sysadmin_settings.tools.bash_timeout = 600  # Longer timeout for system operations
        sysadmin_settings.ui.confirm_destructive = True  # Always confirm dangerous operations
        sysadmin_settings.log_level = "INFO"
        sysadmin_settings.retry.max_retries = 3
        
        self._profiles["sysadmin"] = ConfigProfile(
            name="sysadmin",
            description="System administration focused with enhanced security and logging",
            settings=sysadmin_settings,
            tags=["sysadmin", "security", "ops"]
        )
        
        # Learning Profile
        learning_settings = Settings()
        learning_settings.llm.temperature = 0.6  # Balanced for explanations
        learning_settings.llm.max_tokens = 6144  # Detailed explanations
        learning_settings.ui.show_progress = True  # Show learning progress
        learning_settings.ui.show_timestamps = True  # Track learning sessions
        learning_settings.tools.web_search_enabled = True  # Look up information
        
        self._profiles["learning"] = ConfigProfile(
            name="learning",
            description="Educational focus with detailed explanations and progress tracking",
            settings=learning_settings,
            tags=["learning", "education", "tutorial"]
        )
        
        # Security Audit Profile
        security_settings = Settings()
        security_settings.llm.temperature = 0.1  # Very deterministic for security
        security_settings.tools.bash_enabled = True  # Need system access for audits
        security_settings.tools.bash_timeout = 300
        security_settings.ui.confirm_destructive = True  # Always confirm
        security_settings.log_level = "DEBUG"  # Detailed logging for audits
        security_settings.retry.max_retries = 1  # Don't retry security operations
        
        self._profiles["security"] = ConfigProfile(
            name="security",
            description="Security-focused configuration with enhanced logging and validation",
            settings=security_settings,
            tags=["security", "audit", "compliance"]
        )
        
        # Performance Testing Profile
        perf_settings = Settings()
        perf_settings.llm.temperature = 0.3  # Consistent for testing
        perf_settings.tools.bash_enabled = True  # Need system access
        perf_settings.tools.bash_timeout = 1800  # Long timeout for performance tests
        perf_settings.ui.confirm_destructive = False  # Automated testing
        perf_settings.ui.show_progress = True  # Show test progress
        perf_settings.log_level = "INFO"
        
        self._profiles["performance"] = ConfigProfile(
            name="performance",
            description="Optimized for performance testing and benchmarking",
            settings=perf_settings,
            tags=["performance", "testing", "benchmark"]
        )
        
        # Minimal Profile
        minimal_settings = Settings()
        minimal_settings.llm.max_tokens = 2048  # Shorter responses
        minimal_settings.tools.bash_enabled = False  # Minimal tool access
        minimal_settings.tools.web_search_enabled = False
        minimal_settings.ui.show_progress = False  # Minimal UI
        minimal_settings.ui.confirm_destructive = True  # Still safe
        minimal_settings.log_level = "WARNING"  # Minimal logging
        
        self._profiles["minimal"] = ConfigProfile(
            name="minimal",
            description="Minimal configuration with basic functionality only",
            settings=minimal_settings,
            tags=["minimal", "basic", "simple"]
        )
    
    def get_profile(self, name: str) -> Optional[ConfigProfile]:
        """
        Get configuration profile by name.
        
        Args:
            name: Profile name
            
        Returns:
            Configuration profile or None if not found
        """
        return self._profiles.get(name)
    
    def list_profiles(self) -> Dict[str, ConfigProfile]:
        """
        Get all available profiles.
        
        Returns:
            Dictionary of profile name to ConfigProfile
        """
        return self._profiles.copy()
    
    def find_profiles_by_tag(self, tag: str) -> list[ConfigProfile]:
        """
        Find profiles by tag.
        
        Args:
            tag: Tag to search for
            
        Returns:
            List of matching profiles
        """
        return [
            profile for profile in self._profiles.values()
            if tag in profile.tags
        ]
    
    def create_custom_profile(
        self,
        name: str,
        description: str,
        base_profile: str = "production",
        overrides: Optional[Dict[str, Any]] = None,
        tags: Optional[list[str]] = None
    ) -> ConfigProfile:
        """
        Create a custom profile based on an existing one.
        
        Args:
            name: New profile name
            description: Profile description
            base_profile: Base profile to extend
            overrides: Settings to override
            tags: Profile tags
            
        Returns:
            New configuration profile
        """
        base = self.get_profile(base_profile)
        if not base:
            raise ValueError(f"Base profile '{base_profile}' not found")
        
        # Start with base settings
        new_settings = replace(base.settings)
        
        # Apply overrides
        if overrides:
            for key, value in overrides.items():
                if hasattr(new_settings, key):
                    setattr(new_settings, key, value)
                else:
                    # Handle nested settings
                    parts = key.split('.')
                    if len(parts) == 2:
                        section, setting = parts
                        if hasattr(new_settings, section):
                            section_obj = getattr(new_settings, section)
                            if hasattr(section_obj, setting):
                                setattr(section_obj, setting, value)
        
        # Create new profile
        profile = ConfigProfile(
            name=name,
            description=description,
            settings=new_settings,
            tags=tags or []
        )
        
        # Store profile
        self._profiles[name] = profile
        
        return profile
    
    def save_profile(self, profile: ConfigProfile, file_path: str) -> None:
        """
        Save profile to file.
        
        Args:
            profile: Profile to save
            file_path: File path to save to
        """
        import json
        from pathlib import Path
        
        profile_data = {
            "name": profile.name,
            "description": profile.description,
            "tags": profile.tags,
            "settings": profile.settings.to_dict()
        }
        
        Path(file_path).write_text(json.dumps(profile_data, indent=2))
    
    def load_profile(self, file_path: str) -> ConfigProfile:
        """
        Load profile from file.
        
        Args:
            file_path: File path to load from
            
        Returns:
            Loaded configuration profile
        """
        import json
        from pathlib import Path
        
        profile_data = json.loads(Path(file_path).read_text())
        
        # Create settings from data
        settings = Settings()
        settings_data = profile_data["settings"]
        
        # Apply settings (simplified - would need more robust deserialization)
        for section, values in settings_data.items():
            if hasattr(settings, section) and isinstance(values, dict):
                section_obj = getattr(settings, section)
                for key, value in values.items():
                    if hasattr(section_obj, key):
                        setattr(section_obj, key, value)
        
        profile = ConfigProfile(
            name=profile_data["name"],
            description=profile_data["description"],
            settings=settings,
            tags=profile_data.get("tags", [])
        )
        
        # Store loaded profile
        self._profiles[profile.name] = profile
        
        return profile
    
    def get_profile_recommendations(self, use_case: str) -> list[ConfigProfile]:
        """
        Get profile recommendations based on use case.
        
        Args:
            use_case: Description of intended use case
            
        Returns:
            List of recommended profiles
        """
        use_case_lower = use_case.lower()
        recommendations = []
        
        # Simple keyword matching for recommendations
        if any(word in use_case_lower for word in ["dev", "develop", "code", "program"]):
            recommendations.append(self._profiles["development"])
        
        if any(word in use_case_lower for word in ["research", "study", "analyze", "investigate"]):
            recommendations.append(self._profiles["research"])
        
        if any(word in use_case_lower for word in ["admin", "system", "server", "ops"]):
            recommendations.append(self._profiles["sysadmin"])
        
        if any(word in use_case_lower for word in ["learn", "tutorial", "education", "teach"]):
            recommendations.append(self._profiles["learning"])
        
        if any(word in use_case_lower for word in ["security", "audit", "compliance"]):
            recommendations.append(self._profiles["security"])
        
        if any(word in use_case_lower for word in ["performance", "benchmark", "test"]):
            recommendations.append(self._profiles["performance"])
        
        if any(word in use_case_lower for word in ["minimal", "simple", "basic"]):
            recommendations.append(self._profiles["minimal"])
        
        # Default to production if no specific matches
        if not recommendations:
            recommendations.append(self._profiles["production"])
        
        return recommendations


# Global profile manager instance
profile_manager = ProfileManager()


def get_profile_manager() -> ProfileManager:
    """Get the global profile manager instance."""
    return profile_manager
