{"data_mtime": 1749384199, "dep_lines": [3, 8, 9, 1, 2, 4, 5, 6, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "yaml.error", "yaml.nodes", "datetime", "_typeshed", "types", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc"], "hash": "c864c883c05f3aeff9cbe8e467926c92525ebf90", "id": "yaml.representer", "ignore_all": true, "interface_hash": "94b4b37b27d8600848cfc84e92d412fe1204ea04", "mtime": 1749384193, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml-stubs\\representer.pyi", "plugin_data": null, "size": 3425, "suppressed": [], "version_id": "1.16.0"}