"""
Bash command execution tool.

This tool provides secure bash command execution with real-time output streaming,
timeout handling, and comprehensive security considerations.
"""

import asyncio
import logging
import platform
import re
import time
from typing import Dict, Any, Optional, List, Set

from src.tools.base import BaseTool, ToolResult, ToolStatus
from src.core.exceptions import BashExecutionError, BashTimeoutError
from src.config.settings import get_settings

logger = logging.getLogger(__name__)


class BashTool(BaseTool):
    """
    Bash command execution tool with security and safety features.
    
    Features:
    - Real-time output streaming
    - Configurable timeouts
    - Command validation and security checks
    - Cross-platform support (Windows/Linux/macOS)
    - Detailed execution logging
    """
    
    # Potentially dangerous commands that require extra confirmation
    DANGEROUS_COMMANDS = {
        "rm", "rmdir", "del", "format", "fdisk", "mkfs",
        "dd", "shred", "wipe", "chmod", "chown", "sudo",
        "su", "passwd", "userdel", "groupdel", "shutdown",
        "reboot", "halt", "poweroff", "init", "systemctl",
        "service", "kill", "killall", "pkill", "fuser",
    }
    
    # Commands that modify system state
    SYSTEM_MODIFYING_COMMANDS = {
        "apt", "yum", "dnf", "pacman", "brew", "pip", "npm",
        "yarn", "gem", "cargo", "go", "docker", "kubectl",
        "systemctl", "service", "crontab", "mount", "umount",
    }
    
    def __init__(self, **kwargs) -> None:
        """Initialize bash tool."""
        super().__init__(**kwargs)
        self.settings = get_settings()
        
        # Determine shell based on platform
        if platform.system() == "Windows":
            self.shell = ["powershell", "-Command"]
        else:
            self.shell = ["/bin/bash", "-c"]
    
    @property
    def name(self) -> str:
        """Tool name."""
        return "bash"
    
    @property
    def description(self) -> str:
        """Tool description for LLM."""
        return """Execute bash/shell commands with real-time output.

WHEN TO USE:
- File operations (create, read, modify, delete files and directories)
- System information gathering (ps, df, ls, find, grep, etc.)
- Development tasks (git operations, building, testing)
- Package management (with caution)
- Process management
- Network operations (ping, curl, wget)
- Text processing (sed, awk, sort, uniq)

WHEN NOT TO USE:
- Destructive operations without explicit user permission
- Commands that require interactive input
- Long-running services (use background processes instead)
- Operations that could damage the system

EXECUTION STRATEGY:
- Commands run sequentially by default
- Use parallel execution only for independent, read-only operations
- Always validate command safety before execution
- Provide clear output and error reporting

SECURITY CONSIDERATIONS:
- Commands are validated for safety
- Dangerous operations require confirmation
- Output is limited to prevent memory issues
- Timeouts prevent hanging processes

EXAMPLES:
- File listing: ls -la /path/to/directory
- File creation: echo "content" > file.txt
- Git operations: git status, git add ., git commit -m "message"
- System info: ps aux, df -h, free -m
- Text processing: grep "pattern" file.txt | sort | uniq"""
    
    @property
    def parameters(self) -> Dict[str, Any]:
        """Tool parameters schema."""
        return {
            "type": "object",
            "properties": {
                "command": {
                    "type": "string",
                    "description": "The bash command to execute"
                },
                "working_directory": {
                    "type": "string",
                    "description": "Working directory for command execution (optional)"
                },
                "timeout": {
                    "type": "integer",
                    "description": "Timeout in seconds (optional, default from config)"
                },
                "capture_output": {
                    "type": "boolean",
                    "description": "Whether to capture and return output (default: true)"
                }
            },
            "required": ["command"]
        }
    
    @property
    def can_run_parallel(self) -> bool:
        """Bash commands generally should not run in parallel unless read-only."""
        return False
    
    @property
    def is_destructive(self) -> bool:
        """Bash commands can be destructive."""
        return True
    
    def _is_dangerous_command(self, command: str) -> bool:
        """Check if command contains dangerous operations."""
        # Extract the main command (first word)
        main_command = command.strip().split()[0] if command.strip() else ""
        
        # Remove common prefixes
        for prefix in ["sudo", "su", "doas"]:
            if main_command == prefix and len(command.strip().split()) > 1:
                main_command = command.strip().split()[1]
                break
        
        return main_command in self.DANGEROUS_COMMANDS
    
    def _is_system_modifying(self, command: str) -> bool:
        """Check if command modifies system state."""
        main_command = command.strip().split()[0] if command.strip() else ""
        return main_command in self.SYSTEM_MODIFYING_COMMANDS
    
    def _analyze_command_safety(self, command: str) -> Dict[str, Any]:
        """
        Analyze command for safety and provide recommendations.
        
        Returns:
            Dictionary with safety analysis
        """
        analysis: Dict[str, Any] = {
            "is_dangerous": self._is_dangerous_command(command),
            "is_system_modifying": self._is_system_modifying(command),
            "requires_confirmation": False,
            "warnings": [],
            "recommendations": []
        }
        
        # Check for dangerous patterns
        dangerous_patterns = [
            (r"rm\s+-rf\s+/", "Recursive deletion from root directory"),
            (r"chmod\s+777", "Setting overly permissive file permissions"),
            (r">\s*/dev/sd[a-z]", "Writing directly to disk device"),
            (r"dd\s+.*of=/dev/", "Writing to disk device with dd"),
            (r"mkfs\.", "Creating filesystem (will destroy data)"),
            (r"fdisk", "Disk partitioning tool"),
            (r"shutdown|reboot|halt", "System shutdown/restart"),
        ]
        
        for pattern, warning in dangerous_patterns:
            if re.search(pattern, command, re.IGNORECASE):
                analysis["warnings"].append(warning)
                analysis["requires_confirmation"] = True
        
        # Check for system modification
        if analysis["is_system_modifying"]:
            analysis["warnings"].append("Command may modify system state")
            analysis["recommendations"].append("Consider running with --dry-run first if available")
        
        # Check for dangerous commands
        if analysis["is_dangerous"]:
            analysis["requires_confirmation"] = True
            analysis["warnings"].append("Command contains potentially dangerous operations")
        
        return analysis

    def _translate_unix_to_powershell(self, command: str) -> str:
        """
        Translate common Unix commands to PowerShell equivalents.

        Args:
            command: Unix command to translate

        Returns:
            PowerShell equivalent command
        """
        # Common Unix to PowerShell translations
        translations = {
            # Directory listing
            r'^ls\s*$': 'Get-ChildItem',
            r'^ls\s+-l\s*$': 'Get-ChildItem | Format-Table -AutoSize',
            r'^ls\s+-la\s*$': 'Get-ChildItem -Force | Format-Table -AutoSize',
            r'^ls\s+-a\s*$': 'Get-ChildItem -Force',
            r'^ls\s+(.+)$': r'Get-ChildItem "\1"',

            # Directory operations
            r'^pwd\s*$': 'Get-Location',
            r'^cd\s+(.+)$': r'Set-Location "\1"',
            r'^mkdir\s+(.+)$': r'New-Item -ItemType Directory -Path "\1"',
            r'^rmdir\s+(.+)$': r'Remove-Item -Path "\1" -Recurse',

            # File operations
            r'^cat\s+(.+)$': r'Get-Content "\1"',
            r'^cp\s+(.+)\s+(.+)$': r'Copy-Item "\1" "\2"',
            r'^mv\s+(.+)\s+(.+)$': r'Move-Item "\1" "\2"',
            r'^rm\s+(.+)$': r'Remove-Item "\1"',
            r'^rm\s+-rf\s+(.+)$': r'Remove-Item "\1" -Recurse -Force',

            # Text operations
            r'^echo\s+(.+)$': r'Write-Output \1',
            r'^grep\s+(.+)\s+(.+)$': r'Select-String -Pattern "\1" -Path "\2"',
            r'^find\s+(.+)\s+-name\s+(.+)$': r'Get-ChildItem -Path "\1" -Name "\2" -Recurse',

            # Process operations
            r'^ps\s*$': 'Get-Process',
            r'^ps\s+aux\s*$': 'Get-Process | Format-Table -AutoSize',
            r'^kill\s+(.+)$': r'Stop-Process -Id \1',

            # System info
            r'^df\s*$': 'Get-WmiObject -Class Win32_LogicalDisk | Format-Table -AutoSize',
            r'^df\s+-h\s*$': 'Get-WmiObject -Class Win32_LogicalDisk | Format-Table -AutoSize',
            r'^free\s*$': 'Get-WmiObject -Class Win32_OperatingSystem | Select-Object TotalVisibleMemorySize,FreePhysicalMemory',
            r'^uname\s*$': 'Get-ComputerInfo | Select-Object WindowsProductName,WindowsVersion',

            # Network
            r'^ping\s+(.+)$': r'Test-Connection "\1"',
            r'^wget\s+(.+)$': r'Invoke-WebRequest "\1"',
            r'^curl\s+(.+)$': r'Invoke-RestMethod "\1"',
        }

        import re

        # Try to match and translate the command
        for pattern, replacement in translations.items():
            if re.match(pattern, command.strip(), re.IGNORECASE):
                translated = re.sub(pattern, replacement, command.strip(), flags=re.IGNORECASE)
                logger.debug(f"Translated Unix command '{command}' to PowerShell: '{translated}'")
                return translated

        # If no translation found, return original command
        # PowerShell might still understand it or provide a helpful error
        return command

    async def execute(self, **kwargs: Any) -> ToolResult:
        """
        Execute bash command.
        
        Args:
            command: Command to execute
            working_directory: Working directory (optional)
            timeout: Timeout in seconds (optional)
            capture_output: Whether to capture output (optional)
            
        Returns:
            Tool execution result
        """
        start_time = time.time()
        
        try:
            # Validate parameters
            errors = self.validate_parameters(**kwargs)
            if errors:
                return ToolResult(
                    status=ToolStatus.ERROR,
                    output="",
                    error=f"Parameter validation failed: {', '.join(errors)}",
                    execution_time=time.time() - start_time
                )
            
            command = kwargs["command"]
            working_directory = kwargs.get("working_directory")
            timeout = kwargs.get("timeout", self.settings.tools.bash_timeout)
            capture_output = kwargs.get("capture_output", True)
            
            logger.info(f"Executing bash command: {command}")
            
            # Analyze command safety
            safety_analysis = self._analyze_command_safety(command)
            
            # Prepare command for execution
            if platform.system() == "Windows":
                # On Windows, translate common Unix commands to PowerShell
                translated_command = self._translate_unix_to_powershell(command)
                full_command = ["powershell", "-Command", translated_command]
            else:
                # On Unix-like systems, use bash
                full_command = ["/bin/bash", "-c", command]
            
            # Execute command
            process = await asyncio.create_subprocess_exec(
                *full_command,
                stdout=asyncio.subprocess.PIPE if capture_output else None,
                stderr=asyncio.subprocess.PIPE if capture_output else None,
                cwd=working_directory,
            )
            
            try:
                # Wait for completion with timeout
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=timeout
                )
                
                # Decode output
                stdout_text = stdout.decode("utf-8", errors="replace") if stdout else ""
                stderr_text = stderr.decode("utf-8", errors="replace") if stderr else ""
                
                # Limit output length
                max_lines = self.settings.tools.bash_max_output_lines
                if stdout_text.count('\n') > max_lines:
                    lines = stdout_text.split('\n')
                    stdout_text = '\n'.join(lines[:max_lines]) + f"\n... (output truncated, {len(lines) - max_lines} more lines)"
                
                # Prepare result
                execution_time = time.time() - start_time
                
                if process.returncode == 0:
                    return ToolResult(
                        status=ToolStatus.SUCCESS,
                        output=stdout_text,
                        metadata={
                            "exit_code": process.returncode,
                            "stderr": stderr_text,
                            "command": command,
                            "working_directory": working_directory,
                            "safety_analysis": safety_analysis,
                        },
                        execution_time=execution_time
                    )
                else:
                    error_msg = f"Command failed with exit code {process.returncode}"
                    if stderr_text:
                        error_msg += f": {stderr_text}"
                    
                    return ToolResult(
                        status=ToolStatus.ERROR,
                        output=stdout_text,
                        error=error_msg,
                        metadata={
                            "exit_code": process.returncode,
                            "stderr": stderr_text,
                            "command": command,
                            "working_directory": working_directory,
                            "safety_analysis": safety_analysis,
                        },
                        execution_time=execution_time
                    )
                    
            except asyncio.TimeoutError:
                # Kill the process
                process.kill()
                await process.wait()
                
                raise BashTimeoutError(
                    f"Command timed out after {timeout} seconds",
                    command=command,
                    exit_code=-1
                )
                
        except BashTimeoutError as e:
            return ToolResult(
                status=ToolStatus.TIMEOUT,
                output="",
                error=str(e),
                metadata={"command": command},
                execution_time=time.time() - start_time
            )
        except Exception as e:
            logger.error(f"Bash execution error: {e}")
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Execution failed: {str(e)}",
                metadata={"command": command},
                execution_time=time.time() - start_time
            )
