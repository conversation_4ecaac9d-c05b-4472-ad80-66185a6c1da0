{".class": "MypyFile", "_fullname": "yaml.cyaml", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseConstructor": {".class": "SymbolTableNode", "cross_ref": "yaml.constructor.BaseConstructor", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseRepresenter": {".class": "SymbolTableNode", "cross_ref": "yaml.representer.BaseRepresenter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseResolver": {".class": "SymbolTableNode", "cross_ref": "yaml.resolver.BaseResolver", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CBaseDumper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml._yaml.CEmitter", "yaml.representer.BaseRepresenter", "yaml.resolver.BaseResolver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.cyaml.<PERSON>umper", "name": "CBase<PERSON>umper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.cyaml.<PERSON>umper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.cyaml", "mro": ["yaml.cyaml.<PERSON>umper", "yaml._yaml.CEmitter", "yaml.representer.BaseRepresenter", "yaml.resolver.BaseResolver", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "stream", "default_style", "default_flow_style", "canonical", "indent", "width", "allow_unicode", "line_break", "encoding", "explicit_start", "explicit_end", "version", "tags", "sort_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "yaml.cyaml.CBaseDumper.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "stream", "default_style", "default_flow_style", "canonical", "indent", "width", "allow_unicode", "line_break", "encoding", "explicit_start", "explicit_end", "version", "tags", "sort_keys"], "arg_types": ["yaml.cyaml.<PERSON>umper", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CBaseDumper", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.cyaml.CBaseDumper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.cyaml.<PERSON>umper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CBaseLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml._yaml.<PERSON><PERSON><PERSON>", "yaml.constructor.BaseConstructor", "yaml.resolver.BaseResolver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.cyaml.CBaseLoader", "name": "CBase<PERSON><PERSON>der", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.cyaml.CBaseLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.cyaml", "mro": ["yaml.cyaml.CBaseLoader", "yaml._yaml.<PERSON><PERSON><PERSON>", "yaml.constructor.BaseConstructor", "yaml.resolver.BaseResolver", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "yaml.cyaml.CBaseLoader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["yaml.cyaml.CBaseLoader", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "TypeAliasType", "args": [], "type_ref": "yaml.cyaml._Readable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CBaseLoader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.cyaml.CBaseLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.cyaml.CBaseLoader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CDumper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml._yaml.CEmitter", "yaml.representer.SafeRepresenter", "yaml.resolver.Resolver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.cyaml.<PERSON><PERSON>er", "name": "<PERSON><PERSON>er", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.cyaml.<PERSON><PERSON>er", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.cyaml", "mro": ["yaml.cyaml.<PERSON><PERSON>er", "yaml._yaml.CEmitter", "yaml.representer.SafeRepresenter", "yaml.representer.BaseRepresenter", "yaml.resolver.Resolver", "yaml.resolver.BaseResolver", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "stream", "default_style", "default_flow_style", "canonical", "indent", "width", "allow_unicode", "line_break", "encoding", "explicit_start", "explicit_end", "version", "tags", "sort_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "yaml.cyaml.CDumper.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "stream", "default_style", "default_flow_style", "canonical", "indent", "width", "allow_unicode", "line_break", "encoding", "explicit_start", "explicit_end", "version", "tags", "sort_keys"], "arg_types": ["yaml.cyaml.<PERSON><PERSON>er", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CDumper", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.cyaml.CDumper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.cyaml.<PERSON><PERSON>er", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CEmitter": {".class": "SymbolTableNode", "cross_ref": "yaml._yaml.CEmitter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CFullLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml._yaml.<PERSON><PERSON><PERSON>", "yaml.constructor.FullConstructor", "yaml.resolver.Resolver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.cyaml.<PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.cyaml.<PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.cyaml", "mro": ["yaml.cyaml.<PERSON>", "yaml._yaml.<PERSON><PERSON><PERSON>", "yaml.constructor.FullConstructor", "yaml.constructor.SafeConstructor", "yaml.constructor.BaseConstructor", "yaml.resolver.Resolver", "yaml.resolver.BaseResolver", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "yaml.cyaml.CFullLoader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["yaml.cyaml.<PERSON>", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "TypeAliasType", "args": [], "type_ref": "yaml.cyaml._Readable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CFullLoader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.cyaml.CFullLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.cyaml.<PERSON>", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml._yaml.<PERSON><PERSON><PERSON>", "yaml.constructor.SafeConstructor", "yaml.resolver.Resolver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.cyaml.C<PERSON>der", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.cyaml.C<PERSON>der", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.cyaml", "mro": ["yaml.cyaml.C<PERSON>der", "yaml._yaml.<PERSON><PERSON><PERSON>", "yaml.constructor.SafeConstructor", "yaml.constructor.BaseConstructor", "yaml.resolver.Resolver", "yaml.resolver.BaseResolver", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "yaml.cyaml.CLoader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["yaml.cyaml.C<PERSON>der", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "TypeAliasType", "args": [], "type_ref": "yaml.cyaml._Readable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CLoader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.cyaml.CLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.cyaml.C<PERSON>der", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CParser": {".class": "SymbolTableNode", "cross_ref": "yaml._yaml.<PERSON><PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CSafeDumper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "yaml.cyaml.CSafeDumper", "line": 69, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "yaml.cyaml.<PERSON><PERSON>er"}}, "CSafeLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml._yaml.<PERSON><PERSON><PERSON>", "yaml.constructor.SafeConstructor", "yaml.resolver.Resolver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.cyaml.CSafeLoader", "name": "CSafeLoader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.cyaml.CSafeLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.cyaml", "mro": ["yaml.cyaml.CSafeLoader", "yaml._yaml.<PERSON><PERSON><PERSON>", "yaml.constructor.SafeConstructor", "yaml.constructor.BaseConstructor", "yaml.resolver.Resolver", "yaml.resolver.BaseResolver", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "yaml.cyaml.CSafeLoader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["yaml.cyaml.CSafeLoader", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "TypeAliasType", "args": [], "type_ref": "yaml.cyaml._Readable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CSafeLoader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.cyaml.CSafeLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.cyaml.CSafeLoader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CUnsafeLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["yaml._yaml.<PERSON><PERSON><PERSON>", "yaml.constructor.UnsafeConstructor", "yaml.resolver.Resolver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "yaml.cyaml.CUnsafeLoader", "name": "CUnsafeLoader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "yaml.cyaml.CUnsafeLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "yaml.cyaml", "mro": ["yaml.cyaml.CUnsafeLoader", "yaml._yaml.<PERSON><PERSON><PERSON>", "yaml.constructor.UnsafeConstructor", "yaml.constructor.FullConstructor", "yaml.constructor.SafeConstructor", "yaml.constructor.BaseConstructor", "yaml.resolver.Resolver", "yaml.resolver.BaseResolver", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "yaml.cyaml.CUnsafeLoader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["yaml.cyaml.CUnsafeLoader", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "TypeAliasType", "args": [], "type_ref": "yaml.cyaml._Readable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CUnsafeLoader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "yaml.cyaml.CUnsafeLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "yaml.cyaml.CUnsafeLoader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FullConstructor": {".class": "SymbolTableNode", "cross_ref": "yaml.constructor.FullConstructor", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Resolver": {".class": "SymbolTableNode", "cross_ref": "yaml.resolver.Resolver", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SafeConstructor": {".class": "SymbolTableNode", "cross_ref": "yaml.constructor.SafeConstructor", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SafeRepresenter": {".class": "SymbolTableNode", "cross_ref": "yaml.representer.SafeRepresenter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsRead": {".class": "SymbolTableNode", "cross_ref": "_typeshed.SupportsRead", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UnsafeConstructor": {".class": "SymbolTableNode", "cross_ref": "yaml.constructor.UnsafeConstructor", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CLoader": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "yaml.cyaml._CLoader", "line": 14, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["yaml.cyaml.C<PERSON>der", "yaml.cyaml.CBaseLoader", "yaml.cyaml.<PERSON>", "yaml.cyaml.CSafeLoader", "yaml.cyaml.CUnsafeLoader"], "uses_pep604_syntax": true}}}, "_Readable": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "yaml.cyaml._Readable", "line": 13, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "_typeshed.SupportsRead"}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "yaml.cyaml.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.cyaml.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.cyaml.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.cyaml.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.cyaml.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.cyaml.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yaml.cyaml.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml-stubs\\cyaml.pyi"}