"""
Output formatting utilities for the CLI interface.

This module provides consistent formatting for different types of output
including AI responses, tool results, errors, and system messages.
"""

import re
from datetime import datetime
from typing import Any, Dict, List, Optional

from rich.console import Console
from rich.markdown import Markdown
from rich.panel import Panel
from rich.syntax import Syntax
from rich.table import Table
from rich.text import Text

from src.tools.base import ToolR<PERSON>ult, ToolStatus
from src.config.settings import get_settings


class OutputFormatter:
    """
    Handles formatting and display of various output types.
    
    Provides consistent, readable formatting for AI responses,
    tool results, errors, and system messages.
    """
    
    def __init__(self) -> None:
        """Initialize output formatter."""
        self.settings = get_settings()
        self.console = Console(
            color_system="auto" if self.settings.ui.color_output else None
        )
    
    def format_ai_response(self, content: str, model: Optional[str] = None) -> None:
        """
        Format and display AI response.
        
        Args:
            content: AI response content
            model: Model name (optional)
        """
        # Add timestamp if enabled
        timestamp = ""
        if self.settings.ui.show_timestamps:
            timestamp = f" [{datetime.now().strftime('%H:%M:%S')}]"
        
        # Create title
        title = f"🤖 Arien AI{timestamp}"
        if model:
            title += f" ({model})"
        
        # Format content as markdown
        markdown = Markdown(content)
        
        # Display in panel
        panel = Panel(
            markdown,
            title=title,
            title_align="left",
            border_style="blue",
            padding=(1, 2),
        )
        
        self.console.print(panel)
    
    def format_tool_result(self, tool_name: str, result: ToolResult) -> None:
        """
        Format and display tool execution result.
        
        Args:
            tool_name: Name of the tool
            result: Tool execution result
        """
        # Determine status icon and color
        if result.status == ToolStatus.SUCCESS:
            icon = "✅"
            border_style = "green"
        elif result.status == ToolStatus.ERROR:
            icon = "❌"
            border_style = "red"
        elif result.status == ToolStatus.TIMEOUT:
            icon = "⏰"
            border_style = "yellow"
        else:
            icon = "⚠️"
            border_style = "orange"
        
        # Create title with execution time
        title = f"{icon} {tool_name.title()} Tool"
        if result.execution_time:
            title += f" ({result.execution_time:.2f}s)"
        
        # Prepare content
        content_parts = []
        
        # Add output if available
        if result.output:
            # Try to detect and format code blocks
            formatted_output = self._format_output_content(result.output)
            content_parts.append(formatted_output)
        
        # Add error if present
        if result.error:
            error_text = Text(f"Error: {result.error}", style="red")
            content_parts.append(error_text)
        
        # Add metadata if available and relevant
        if result.metadata:
            metadata_text = self._format_metadata(result.metadata)
            if metadata_text:
                content_parts.append(metadata_text)
        
        # Combine content
        if content_parts:
            content = "\n\n".join(str(part) for part in content_parts)
        else:
            content = "No output"
        
        # Display in panel
        panel = Panel(
            content,
            title=title,
            title_align="left",
            border_style=border_style,
            padding=(1, 2),
        )
        
        self.console.print(panel)
    
    def format_error(self, error: str, title: str = "Error") -> None:
        """
        Format and display error message.
        
        Args:
            error: Error message
            title: Error title
        """
        panel = Panel(
            Text(error, style="red"),
            title=f"❌ {title}",
            title_align="left",
            border_style="red",
            padding=(1, 2),
        )
        
        self.console.print(panel)
    
    def format_warning(self, warning: str, title: str = "Warning") -> None:
        """
        Format and display warning message.
        
        Args:
            warning: Warning message
            title: Warning title
        """
        panel = Panel(
            Text(warning, style="yellow"),
            title=f"⚠️ {title}",
            title_align="left",
            border_style="yellow",
            padding=(1, 2),
        )
        
        self.console.print(panel)
    
    def format_info(self, info: str, title: str = "Info") -> None:
        """
        Format and display info message.
        
        Args:
            info: Info message
            title: Info title
        """
        panel = Panel(
            Text(info, style="blue"),
            title=f"ℹ️ {title}",
            title_align="left",
            border_style="blue",
            padding=(1, 2),
        )
        
        self.console.print(panel)
    
    def format_success(self, message: str, title: str = "Success") -> None:
        """
        Format and display success message.
        
        Args:
            message: Success message
            title: Success title
        """
        panel = Panel(
            Text(message, style="green"),
            title=f"✅ {title}",
            title_align="left",
            border_style="green",
            padding=(1, 2),
        )
        
        self.console.print(panel)
    
    def format_user_input(self, prompt: str) -> str:
        """
        Format user input prompt.
        
        Args:
            prompt: Input prompt
            
        Returns:
            User input
        """
        return self.console.input(f"[bold blue]👤 {prompt}[/bold blue] ")
    
    def format_confirmation(self, message: str, default: bool = False) -> bool:
        """
        Format confirmation prompt.
        
        Args:
            message: Confirmation message
            default: Default value if user just presses Enter
            
        Returns:
            User's confirmation choice
        """
        default_text = "Y/n" if default else "y/N"
        response = self.console.input(f"[bold yellow]❓ {message} ({default_text})[/bold yellow] ")
        
        if not response.strip():
            return default
        
        return response.strip().lower() in ["y", "yes", "true", "1"]
    
    def _format_output_content(self, content: str) -> Any:
        """Format output content with syntax highlighting if applicable."""
        # Try to detect code blocks or structured content
        
        # Check for JSON
        if self._looks_like_json(content):
            try:
                return Syntax(content, "json", theme="monokai", line_numbers=False)
            except:
                pass
        
        # Check for XML/HTML
        if self._looks_like_xml(content):
            try:
                return Syntax(content, "xml", theme="monokai", line_numbers=False)
            except:
                pass
        
        # Check for code patterns
        if self._looks_like_code(content):
            # Try to detect language
            language = self._detect_language(content)
            try:
                return Syntax(content, language, theme="monokai", line_numbers=False)
            except:
                pass
        
        # Default to plain text
        return content
    
    def _format_metadata(self, metadata: Dict[str, Any]) -> Optional[Text]:
        """Format metadata for display."""
        if not metadata:
            return None
        
        # Filter out sensitive or uninteresting metadata
        filtered_metadata = {}
        for key, value in metadata.items():
            if key not in ["api_key", "token", "password"]:
                if isinstance(value, (str, int, float, bool)):
                    filtered_metadata[key] = value
        
        if not filtered_metadata:
            return None
        
        # Format as key-value pairs
        lines = []
        for key, value in filtered_metadata.items():
            lines.append(f"{key}: {value}")
        
        return Text("\n".join(lines), style="dim")
    
    def _looks_like_json(self, content: str) -> bool:
        """Check if content looks like JSON."""
        stripped = content.strip()
        return (stripped.startswith("{") and stripped.endswith("}")) or \
               (stripped.startswith("[") and stripped.endswith("]"))
    
    def _looks_like_xml(self, content: str) -> bool:
        """Check if content looks like XML/HTML."""
        stripped = content.strip()
        return stripped.startswith("<") and stripped.endswith(">")
    
    def _looks_like_code(self, content: str) -> bool:
        """Check if content looks like code."""
        # Simple heuristics for code detection
        code_indicators = [
            "def ", "class ", "import ", "from ",  # Python
            "function ", "var ", "const ", "let ",  # JavaScript
            "public ", "private ", "class ", "interface ",  # Java/C#
            "#include", "int main", "void ",  # C/C++
            "SELECT ", "INSERT ", "UPDATE ", "DELETE ",  # SQL
        ]
        
        content_lower = content.lower()
        return any(indicator in content_lower for indicator in code_indicators)
    
    def _detect_language(self, content: str) -> str:
        """Detect programming language from content."""
        content_lower = content.lower()
        
        # Python indicators
        if any(keyword in content_lower for keyword in ["def ", "import ", "from ", "class ", "if __name__"]):
            return "python"
        
        # JavaScript indicators
        if any(keyword in content_lower for keyword in ["function ", "var ", "const ", "let ", "=>"]):
            return "javascript"
        
        # SQL indicators
        if any(keyword in content_lower for keyword in ["select ", "insert ", "update ", "delete ", "create table"]):
            return "sql"
        
        # Shell/Bash indicators
        if any(keyword in content_lower for keyword in ["#!/bin/bash", "echo ", "ls ", "cd ", "mkdir "]):
            return "bash"
        
        # Default to text
        return "text"
    
    def print(self, *args, **kwargs) -> None:
        """Print using rich console."""
        self.console.print(*args, **kwargs)
    
    def clear(self) -> None:
        """Clear the console."""
        self.console.clear()
