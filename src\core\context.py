"""
Context management for Arien AI.

This module provides conversation context, session management,
and intelligent context-aware prompting capabilities.
"""

import json
import time
from dataclasses import dataclass, asdict
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from uuid import uuid4

from src.providers.base import Message, MessageRole
from src.tools.base import Tool<PERSON><PERSON>ult
from src.config.settings import get_settings


@dataclass
class ConversationTurn:
    """Represents a single turn in the conversation."""
    id: str
    timestamp: float
    user_input: str
    ai_response: str
    tool_executions: List[Dict[str, Any]]
    metadata: Dict[str, Any]


@dataclass
class SessionInfo:
    """Information about the current session."""
    session_id: str
    start_time: float
    user_id: Optional[str] = None
    working_directory: Optional[str] = None
    environment: Optional[Dict[str, str]] = None
    preferences: Optional[Dict[str, Any]] = None


class ConversationContext:
    """
    Manages conversation context and history.
    
    Provides intelligent context management with memory optimization,
    context summarization, and relevant history retrieval.
    """
    
    def __init__(self, max_history: int = 50, max_tokens: int = 8000):
        """
        Initialize conversation context.
        
        Args:
            max_history: Maximum number of conversation turns to keep
            max_tokens: Maximum tokens to use for context
        """
        self.max_history = max_history
        self.max_tokens = max_tokens
        self.messages: List[Message] = []
        self.conversation_turns: List[ConversationTurn] = []
        self.session_info: Optional[SessionInfo] = None
        self.context_summary: Optional[str] = None
        
        # Initialize session
        self._initialize_session()
    
    def _initialize_session(self) -> None:
        """Initialize a new session."""
        import os
        
        self.session_info = SessionInfo(
            session_id=str(uuid4()),
            start_time=time.time(),
            working_directory=os.getcwd(),
            environment=dict(os.environ),
            preferences={}
        )
    
    def add_system_message(self, content: str) -> None:
        """Add a system message to the context."""
        message = Message(role=MessageRole.SYSTEM, content=content)
        self.messages.append(message)
    
    def add_user_message(self, content: str) -> None:
        """Add a user message to the context."""
        message = Message(role=MessageRole.USER, content=content)
        self.messages.append(message)
    
    def add_assistant_message(
        self,
        content: str,
        tool_calls: Optional[List[Dict[str, Any]]] = None
    ) -> None:
        """Add an assistant message to the context."""
        message = Message(
            role=MessageRole.ASSISTANT,
            content=content,
            tool_calls=tool_calls
        )
        self.messages.append(message)
    
    def add_tool_message(
        self,
        tool_name: str,
        result: ToolResult,
        tool_call_id: str
    ) -> None:
        """Add a tool execution result to the context."""
        content = json.dumps({
            "status": result.status.value,
            "output": result.output,
            "error": result.error,
            "metadata": result.metadata,
        })
        
        message = Message(
            role=MessageRole.TOOL,
            content=content,
            tool_call_id=tool_call_id,
            name=tool_name
        )
        self.messages.append(message)
    
    def add_conversation_turn(
        self,
        user_input: str,
        ai_response: str,
        tool_executions: List[Dict[str, Any]],
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Add a complete conversation turn."""
        turn = ConversationTurn(
            id=str(uuid4()),
            timestamp=time.time(),
            user_input=user_input,
            ai_response=ai_response,
            tool_executions=tool_executions,
            metadata=metadata or {}
        )
        
        self.conversation_turns.append(turn)
        
        # Trim history if needed
        if len(self.conversation_turns) > self.max_history:
            self.conversation_turns = self.conversation_turns[-self.max_history:]
    
    def get_context_messages(self) -> List[Message]:
        """
        Get context messages optimized for token usage.

        Returns:
            List of messages within token limit
        """
        if not self.messages:
            return []

        # Separate system messages and other messages
        system_messages = [msg for msg in self.messages if msg.role == MessageRole.SYSTEM]
        other_messages = [msg for msg in self.messages if msg.role != MessageRole.SYSTEM]

        # Estimate tokens for system messages
        current_tokens = sum(len(msg.content) // 4 for msg in system_messages)

        # Add recent messages within token limit, maintaining chronological order
        selected_messages: List[Message] = []
        for message in reversed(other_messages):
            message_tokens = len(message.content) // 4
            if current_tokens + message_tokens <= self.max_tokens:
                selected_messages.insert(0, message)  # Insert at beginning to maintain order
                current_tokens += message_tokens
            else:
                break

        # Combine system messages + selected other messages in proper order
        return system_messages + selected_messages
    
    def get_relevant_history(self, query: str, max_turns: int = 5) -> List[ConversationTurn]:
        """
        Get conversation history relevant to the current query.
        
        Args:
            query: Current user query
            max_turns: Maximum number of turns to return
            
        Returns:
            List of relevant conversation turns
        """
        if not self.conversation_turns:
            return []
        
        # Simple relevance scoring based on keyword overlap
        query_words = set(query.lower().split())
        scored_turns = []
        
        for turn in self.conversation_turns:
            # Score based on input and response content
            turn_words = set(turn.user_input.lower().split()) | set(turn.ai_response.lower().split())
            overlap = len(query_words & turn_words)
            
            # Boost score for recent turns
            recency_boost = 1.0 / (1.0 + (time.time() - turn.timestamp) / 3600)  # Decay over hours
            
            score = overlap + recency_boost
            scored_turns.append((score, turn))
        
        # Sort by score and return top turns
        scored_turns.sort(key=lambda x: x[0], reverse=True)
        return [turn for _, turn in scored_turns[:max_turns]]
    
    def get_working_context(self) -> Dict[str, Any]:
        """
        Get current working context information.
        
        Returns:
            Dictionary with current context
        """
        import os
        
        context = {
            "session_id": self.session_info.session_id if self.session_info else None,
            "working_directory": os.getcwd(),
            "conversation_length": len(self.conversation_turns),
            "recent_tools": self._get_recent_tools(),
            "session_duration": time.time() - (self.session_info.start_time if self.session_info else time.time()),
        }
        
        return context
    
    def _get_recent_tools(self, limit: int = 5) -> List[str]:
        """Get list of recently used tools."""
        recent_tools = []
        
        for turn in reversed(self.conversation_turns[-10:]):  # Look at last 10 turns
            for execution in turn.tool_executions:
                tool_name = execution.get("tool_name")
                if tool_name and tool_name not in recent_tools:
                    recent_tools.append(tool_name)
                    if len(recent_tools) >= limit:
                        break
            if len(recent_tools) >= limit:
                break
        
        return recent_tools
    
    def generate_context_summary(self) -> str:
        """
        Generate a summary of the conversation context.
        
        Returns:
            Context summary string
        """
        if not self.conversation_turns:
            return "No conversation history."
        
        # Analyze conversation patterns
        total_turns = len(self.conversation_turns)
        tools_used = set()
        topics = []
        
        for turn in self.conversation_turns:
            # Extract tools used
            for execution in turn.tool_executions:
                tools_used.add(execution.get("tool_name", "unknown"))
            
            # Extract potential topics (simple keyword extraction)
            words = turn.user_input.lower().split()
            topics.extend([word for word in words if len(word) > 4])
        
        # Generate summary
        summary_parts = [
            f"Conversation with {total_turns} turns",
        ]
        
        if tools_used:
            summary_parts.append(f"Tools used: {', '.join(sorted(tools_used))}")
        
        if topics:
            # Get most common topics
            from collections import Counter
            common_topics = Counter(topics).most_common(3)
            topic_str = ', '.join([topic for topic, _ in common_topics])
            summary_parts.append(f"Main topics: {topic_str}")
        
        return ". ".join(summary_parts) + "."
    
    def save_session(self, file_path: Optional[Union[str, Path]] = None) -> None:
        """
        Save session to file.
        
        Args:
            file_path: Optional file path, defaults to session-based name
        """
        if file_path is None:
            settings = get_settings()
            session_dir = settings.config_dir / "sessions"
            session_dir.mkdir(exist_ok=True)
            session_id = self.session_info.session_id if self.session_info else "unknown"
            file_path = session_dir / f"session_{session_id}.json"
        
        session_data = {
            "session_info": asdict(self.session_info) if self.session_info else None,
            "conversation_turns": [asdict(turn) for turn in self.conversation_turns],
            "context_summary": self.context_summary,
            "saved_at": time.time(),
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(session_data, f, indent=2, ensure_ascii=False)
    
    def load_session(self, file_path: Union[str, Path]) -> None:
        """
        Load session from file.
        
        Args:
            file_path: Path to session file
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            session_data = json.load(f)
        
        # Restore session info
        if session_data.get("session_info"):
            self.session_info = SessionInfo(**session_data["session_info"])
        
        # Restore conversation turns
        self.conversation_turns = [
            ConversationTurn(**turn_data)
            for turn_data in session_data.get("conversation_turns", [])
        ]
        
        # Restore context summary
        self.context_summary = session_data.get("context_summary")
    
    def clear_context(self, keep_system: bool = True) -> None:
        """
        Clear conversation context.
        
        Args:
            keep_system: Whether to keep system messages
        """
        if keep_system:
            system_messages = [msg for msg in self.messages if msg.role == MessageRole.SYSTEM]
            self.messages = system_messages
        else:
            self.messages.clear()
        
        self.conversation_turns.clear()
        self.context_summary = None
    
    def get_context_stats(self) -> Dict[str, Any]:
        """Get statistics about the current context."""
        return {
            "total_messages": len(self.messages),
            "total_turns": len(self.conversation_turns),
            "session_duration": time.time() - (self.session_info.start_time if self.session_info else time.time()),
            "tools_used": len(set(
                execution.get("tool_name")
                for turn in self.conversation_turns
                for execution in turn.tool_executions
                if execution.get("tool_name")
            )),
            "estimated_tokens": sum(len(msg.content) // 4 for msg in self.messages),
        }
