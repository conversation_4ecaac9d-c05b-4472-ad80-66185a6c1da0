"""
File operations tool for Arien AI.

This tool provides safe file and directory operations with comprehensive
security checks and user-friendly interfaces.
"""

import os
import shutil
import time
from pathlib import Path
from typing import Dict, Any, List, Optional, Union

from src.tools.base import BaseTool, ToolResult, ToolStatus
from src.core.exceptions import ToolError
from src.config.settings import get_settings

import logging
logger = logging.getLogger(__name__)


class FileTool(BaseTool):
    """
    File operations tool with security and safety features.
    
    Features:
    - Safe file and directory operations
    - Path validation and security checks
    - Backup creation for destructive operations
    - Comprehensive error handling
    - Support for various file formats
    """
    
    # Dangerous paths that should be protected
    PROTECTED_PATHS = {
        "/", "/bin", "/boot", "/dev", "/etc", "/lib", "/lib64",
        "/proc", "/root", "/sbin", "/sys", "/usr", "/var",
        "C:\\", "C:\\Windows", "C:\\Program Files", "C:\\Program Files (x86)",
        "/System", "/Applications", "/Library"
    }
    
    # File extensions that require extra caution
    EXECUTABLE_EXTENSIONS = {
        ".exe", ".bat", ".cmd", ".com", ".scr", ".pif",
        ".sh", ".bash", ".zsh", ".fish", ".ps1", ".vbs",
        ".js", ".jar", ".app", ".dmg", ".pkg"
    }
    
    def __init__(self, **kwargs) -> None:
        """Initialize file tool."""
        super().__init__(**kwargs)
        self.settings = get_settings()
    
    @property
    def name(self) -> str:
        """Tool name."""
        return "file"
    
    @property
    def description(self) -> str:
        """Tool description for LLM."""
        return """Perform safe file and directory operations.

WHEN TO USE:
- Reading file contents
- Writing or creating files
- Copying, moving, or deleting files
- Creating or removing directories
- Listing directory contents
- Checking file/directory properties
- Searching for files

WHEN NOT TO USE:
- For system files or protected directories
- For executable files without explicit permission
- For operations that could damage the system

OPERATIONS AVAILABLE:
- read: Read file contents
- write: Write content to file
- append: Append content to file
- copy: Copy files or directories
- move: Move/rename files or directories
- delete: Delete files or directories
- mkdir: Create directories
- list: List directory contents
- exists: Check if path exists
- info: Get file/directory information
- search: Search for files

SECURITY FEATURES:
- Path validation and sanitization
- Protection of system directories
- Backup creation for destructive operations
- File type validation
- Size limits for operations

EXAMPLES:
- Read a file: {"operation": "read", "path": "config.txt"}
- Write to file: {"operation": "write", "path": "output.txt", "content": "Hello"}
- List directory: {"operation": "list", "path": ".", "pattern": "*.py"}
- Copy file: {"operation": "copy", "source": "file.txt", "destination": "backup.txt"}
"""
    
    @property
    def parameters(self) -> Dict[str, Any]:
        """Tool parameters schema."""
        return {
            "type": "object",
            "properties": {
                "operation": {
                    "type": "string",
                    "enum": ["read", "write", "append", "copy", "move", "delete", "mkdir", "list", "exists", "info", "search"],
                    "description": "File operation to perform"
                },
                "path": {
                    "type": "string",
                    "description": "File or directory path"
                },
                "content": {
                    "type": "string",
                    "description": "Content to write (for write/append operations)"
                },
                "source": {
                    "type": "string",
                    "description": "Source path (for copy/move operations)"
                },
                "destination": {
                    "type": "string",
                    "description": "Destination path (for copy/move operations)"
                },
                "pattern": {
                    "type": "string",
                    "description": "Search pattern (for list/search operations)"
                },
                "recursive": {
                    "type": "boolean",
                    "description": "Recursive operation (default: false)"
                },
                "create_backup": {
                    "type": "boolean",
                    "description": "Create backup before destructive operations (default: true)"
                },
                "encoding": {
                    "type": "string",
                    "description": "File encoding (default: utf-8)"
                }
            },
            "required": ["operation"]
        }
    
    @property
    def can_run_parallel(self) -> bool:
        """File operations should generally be sequential for safety."""
        return False
    
    @property
    def is_destructive(self) -> bool:
        """File operations can be destructive."""
        return True
    
    def _validate_path(self, path: Union[str, Path]) -> Path:
        """
        Validate and sanitize file path.

        Args:
            path: Path to validate

        Returns:
            Validated Path object

        Raises:
            ToolError: If path is invalid or dangerous
        """
        try:
            path_obj = Path(path).resolve()
        except Exception as e:
            raise ToolError(f"Invalid path: {e}")

        # Check for dangerous patterns first
        if ".." in str(path):
            raise ToolError("Path traversal not allowed")

        # Check for protected paths, but allow temp directories
        path_str = str(path_obj)

        # Allow access to temp directories
        import tempfile
        temp_dir = Path(tempfile.gettempdir()).resolve()
        if path_obj.is_relative_to(temp_dir):
            return path_obj

        # Allow access to user home directory and subdirectories
        home_dir = Path.home().resolve()
        if path_obj.is_relative_to(home_dir):
            return path_obj

        # Allow access to current working directory and subdirectories
        cwd = Path.cwd().resolve()
        if path_obj.is_relative_to(cwd):
            return path_obj

        # Check for protected system paths
        for protected in self.PROTECTED_PATHS:
            if path_str.startswith(protected) and not path_obj.is_relative_to(temp_dir):
                raise ToolError(f"Access to protected path denied: {protected}")

        return path_obj
    
    def _is_executable(self, path: Path) -> bool:
        """Check if file is executable."""
        return path.suffix.lower() in self.EXECUTABLE_EXTENSIONS
    
    def _create_backup(self, path: Path) -> Optional[Path]:
        """
        Create backup of file before destructive operation.
        
        Args:
            path: Path to backup
            
        Returns:
            Backup path if created, None otherwise
        """
        if not path.exists():
            return None
        
        timestamp = int(time.time())
        backup_path = path.with_suffix(f"{path.suffix}.backup.{timestamp}")
        
        try:
            if path.is_file():
                shutil.copy2(path, backup_path)
            elif path.is_dir():
                shutil.copytree(path, backup_path)
            
            logger.info(f"Created backup: {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.warning(f"Failed to create backup: {e}")
            return None
    
    async def execute(self, **kwargs) -> ToolResult:
        """
        Execute file operation.
        
        Args:
            operation: Operation to perform
            **kwargs: Operation-specific parameters
            
        Returns:
            Tool execution result
        """
        start_time = time.time()
        
        try:
            # Validate parameters
            errors = self.validate_parameters(**kwargs)
            if errors:
                return ToolResult(
                    status=ToolStatus.ERROR,
                    output="",
                    error=f"Parameter validation failed: {', '.join(errors)}",
                    execution_time=time.time() - start_time
                )
            
            operation = kwargs["operation"]
            
            # Route to specific operation handler
            handlers = {
                "read": self._handle_read,
                "write": self._handle_write,
                "append": self._handle_append,
                "copy": self._handle_copy,
                "move": self._handle_move,
                "delete": self._handle_delete,
                "mkdir": self._handle_mkdir,
                "list": self._handle_list,
                "exists": self._handle_exists,
                "info": self._handle_info,
                "search": self._handle_search,
            }
            
            handler = handlers.get(operation)
            if not handler:
                return ToolResult(
                    status=ToolStatus.ERROR,
                    output="",
                    error=f"Unknown operation: {operation}",
                    execution_time=time.time() - start_time
                )
            
            # Execute operation
            result = await handler(**kwargs)
            result.execution_time = time.time() - start_time
            return result
            
        except Exception as e:
            logger.error(f"File operation error: {e}")
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Operation failed: {str(e)}",
                execution_time=time.time() - start_time
            )
    
    async def _handle_read(self, **kwargs) -> ToolResult:
        """Handle file read operation."""
        path = self._validate_path(kwargs["path"])
        encoding = kwargs.get("encoding", "utf-8")
        
        if not path.exists():
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"File not found: {path}"
            )
        
        if not path.is_file():
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Path is not a file: {path}"
            )
        
        try:
            content = path.read_text(encoding=encoding)
            return ToolResult(
                status=ToolStatus.SUCCESS,
                output=content,
                metadata={
                    "path": str(path),
                    "size": path.stat().st_size,
                    "encoding": encoding
                }
            )
        except UnicodeDecodeError:
            # Try binary read for non-text files
            try:
                content = path.read_bytes()
                return ToolResult(
                    status=ToolStatus.SUCCESS,
                    output=f"Binary file ({len(content)} bytes)",
                    metadata={
                        "path": str(path),
                        "size": len(content),
                        "binary": True
                    }
                )
            except Exception as e:
                return ToolResult(
                    status=ToolStatus.ERROR,
                    output="",
                    error=f"Failed to read file: {e}"
                )
    
    async def _handle_write(self, **kwargs) -> ToolResult:
        """Handle file write operation."""
        path = self._validate_path(kwargs["path"])
        content = kwargs.get("content", "")
        encoding = kwargs.get("encoding", "utf-8")
        create_backup = kwargs.get("create_backup", True)
        
        # Create backup if file exists
        backup_path = None
        if create_backup and path.exists():
            backup_path = self._create_backup(path)
        
        try:
            # Create parent directories if needed
            path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write content
            path.write_text(content, encoding=encoding)
            
            return ToolResult(
                status=ToolStatus.SUCCESS,
                output=f"Successfully wrote {len(content)} characters to {path}",
                metadata={
                    "path": str(path),
                    "size": len(content),
                    "encoding": encoding,
                    "backup_path": str(backup_path) if backup_path else None
                }
            )
            
        except Exception as e:
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Failed to write file: {e}"
            )
    
    async def _handle_append(self, **kwargs) -> ToolResult:
        """Handle file append operation."""
        path = self._validate_path(kwargs["path"])
        content = kwargs.get("content", "")
        encoding = kwargs.get("encoding", "utf-8")
        
        try:
            # Create parent directories if needed
            path.parent.mkdir(parents=True, exist_ok=True)
            
            # Append content
            with open(path, 'a', encoding=encoding) as f:
                f.write(content)
            
            return ToolResult(
                status=ToolStatus.SUCCESS,
                output=f"Successfully appended {len(content)} characters to {path}",
                metadata={
                    "path": str(path),
                    "appended_size": len(content),
                    "total_size": path.stat().st_size,
                    "encoding": encoding
                }
            )
            
        except Exception as e:
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Failed to append to file: {e}"
            )
    
    async def _handle_copy(self, **kwargs) -> ToolResult:
        """Handle file/directory copy operation."""
        source = self._validate_path(kwargs["source"])
        destination = self._validate_path(kwargs["destination"])
        
        if not source.exists():
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Source not found: {source}"
            )
        
        try:
            if source.is_file():
                # Create parent directories if needed
                destination.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(source, destination)
                operation_type = "file"
            elif source.is_dir():
                shutil.copytree(source, destination)
                operation_type = "directory"
            else:
                return ToolResult(
                    status=ToolStatus.ERROR,
                    output="",
                    error=f"Unknown file type: {source}"
                )
            
            return ToolResult(
                status=ToolStatus.SUCCESS,
                output=f"Successfully copied {operation_type} from {source} to {destination}",
                metadata={
                    "source": str(source),
                    "destination": str(destination),
                    "type": operation_type
                }
            )
            
        except Exception as e:
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Failed to copy: {e}"
            )
    
    async def _handle_move(self, **kwargs) -> ToolResult:
        """Handle file/directory move operation."""
        source = self._validate_path(kwargs["source"])
        destination = self._validate_path(kwargs["destination"])
        create_backup = kwargs.get("create_backup", True)
        
        if not source.exists():
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Source not found: {source}"
            )
        
        # Create backup if destination exists
        backup_path = None
        if create_backup and destination.exists():
            backup_path = self._create_backup(destination)
        
        try:
            # Create parent directories if needed
            destination.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.move(source, destination)
            
            return ToolResult(
                status=ToolStatus.SUCCESS,
                output=f"Successfully moved {source} to {destination}",
                metadata={
                    "source": str(source),
                    "destination": str(destination),
                    "backup_path": str(backup_path) if backup_path else None
                }
            )
            
        except Exception as e:
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Failed to move: {e}"
            )
    
    async def _handle_delete(self, **kwargs) -> ToolResult:
        """Handle file/directory delete operation."""
        path = self._validate_path(kwargs["path"])
        create_backup = kwargs.get("create_backup", True)
        
        if not path.exists():
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Path not found: {path}"
            )
        
        # Create backup before deletion
        backup_path = None
        if create_backup:
            backup_path = self._create_backup(path)
        
        try:
            if path.is_file():
                path.unlink()
                operation_type = "file"
            elif path.is_dir():
                shutil.rmtree(path)
                operation_type = "directory"
            else:
                return ToolResult(
                    status=ToolStatus.ERROR,
                    output="",
                    error=f"Unknown file type: {path}"
                )
            
            return ToolResult(
                status=ToolStatus.SUCCESS,
                output=f"Successfully deleted {operation_type}: {path}",
                metadata={
                    "path": str(path),
                    "type": operation_type,
                    "backup_path": str(backup_path) if backup_path else None
                }
            )
            
        except Exception as e:
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Failed to delete: {e}"
            )
    
    async def _handle_mkdir(self, **kwargs) -> ToolResult:
        """Handle directory creation operation."""
        path = self._validate_path(kwargs["path"])
        
        try:
            path.mkdir(parents=True, exist_ok=True)
            
            return ToolResult(
                status=ToolStatus.SUCCESS,
                output=f"Successfully created directory: {path}",
                metadata={"path": str(path)}
            )
            
        except Exception as e:
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Failed to create directory: {e}"
            )
    
    async def _handle_list(self, **kwargs) -> ToolResult:
        """Handle directory listing operation."""
        path = self._validate_path(kwargs["path"])
        pattern = kwargs.get("pattern", "*")
        recursive = kwargs.get("recursive", False)
        
        if not path.exists():
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Path not found: {path}"
            )
        
        if not path.is_dir():
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Path is not a directory: {path}"
            )
        
        try:
            if recursive:
                items = list(path.rglob(pattern))
            else:
                items = list(path.glob(pattern))
            
            # Format output
            output_lines = []
            for item in sorted(items):
                relative_path = item.relative_to(path)
                if item.is_dir():
                    output_lines.append(f"📁 {relative_path}/")
                else:
                    size = item.stat().st_size
                    output_lines.append(f"📄 {relative_path} ({size} bytes)")
            
            output = "\n".join(output_lines) if output_lines else "No items found"
            
            return ToolResult(
                status=ToolStatus.SUCCESS,
                output=output,
                metadata={
                    "path": str(path),
                    "pattern": pattern,
                    "recursive": recursive,
                    "count": len(items)
                }
            )
            
        except Exception as e:
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Failed to list directory: {e}"
            )
    
    async def _handle_exists(self, **kwargs) -> ToolResult:
        """Handle path existence check."""
        path = self._validate_path(kwargs["path"])
        
        exists = path.exists()
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            output=f"Path {'exists' if exists else 'does not exist'}: {path}",
            metadata={
                "path": str(path),
                "exists": exists
            }
        )
    
    async def _handle_info(self, **kwargs) -> ToolResult:
        """Handle file/directory info operation."""
        path = self._validate_path(kwargs["path"])
        
        if not path.exists():
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Path not found: {path}"
            )
        
        try:
            stat = path.stat()
            
            info = {
                "path": str(path),
                "type": "directory" if path.is_dir() else "file",
                "size": stat.st_size,
                "modified": time.ctime(stat.st_mtime),
                "created": time.ctime(stat.st_ctime),
                "permissions": oct(stat.st_mode)[-3:],
            }
            
            if path.is_file():
                info["extension"] = path.suffix
                info["is_executable"] = self._is_executable(path)
            
            # Format output
            output_lines = []
            for key, value in info.items():
                output_lines.append(f"{key.title()}: {value}")
            
            return ToolResult(
                status=ToolStatus.SUCCESS,
                output="\n".join(output_lines),
                metadata=info
            )
            
        except Exception as e:
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Failed to get file info: {e}"
            )
    
    async def _handle_search(self, **kwargs) -> ToolResult:
        """Handle file search operation."""
        path = self._validate_path(kwargs["path"])
        pattern = kwargs.get("pattern", "*")
        
        if not path.exists():
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Path not found: {path}"
            )
        
        if not path.is_dir():
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Path is not a directory: {path}"
            )
        
        try:
            # Recursive search
            items = list(path.rglob(pattern))
            
            # Format output
            output_lines = []
            for item in sorted(items):
                relative_path = item.relative_to(path)
                if item.is_dir():
                    output_lines.append(f"📁 {relative_path}/")
                else:
                    size = item.stat().st_size
                    output_lines.append(f"📄 {relative_path} ({size} bytes)")
            
            output = "\n".join(output_lines) if output_lines else f"No files matching '{pattern}' found"
            
            return ToolResult(
                status=ToolStatus.SUCCESS,
                output=output,
                metadata={
                    "path": str(path),
                    "pattern": pattern,
                    "count": len(items)
                }
            )
            
        except Exception as e:
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Failed to search: {e}"
            )
