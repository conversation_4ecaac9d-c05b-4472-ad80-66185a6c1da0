{"data_mtime": 1749384199, "dep_lines": [1, 4, 6, 2, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "yaml.error", "yaml.events", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "d4f3f660181758630bf39dabced965b57f67e331", "id": "yaml.emitter", "ignore_all": true, "interface_hash": "251a085325f2281296e19dd4027a59b950fb8310", "mtime": 1749384193, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml-stubs\\emitter.pyi", "plugin_data": null, "size": 5231, "suppressed": [], "version_id": "1.16.0"}