{".class": "MypyFile", "_fullname": "httpcore._sync.socks_proxy", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AUTH_METHODS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "httpcore._sync.socks_proxy.AUTH_METHODS", "name": "AUTH_METHODS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.bytes", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "ConnectionInterface": {".class": "SymbolTableNode", "cross_ref": "httpcore._sync.interfaces.ConnectionInterface", "kind": "Gdef"}, "ConnectionNotAvailable": {".class": "SymbolTableNode", "cross_ref": "httpcore._exceptions.ConnectionNotAvailable", "kind": "Gdef"}, "ConnectionPool": {".class": "SymbolTableNode", "cross_ref": "httpcore._sync.connection_pool.ConnectionPool", "kind": "Gdef"}, "HTTP11Connection": {".class": "SymbolTableNode", "cross_ref": "httpcore._sync.http11.HTTP11Connection", "kind": "Gdef"}, "Lock": {".class": "SymbolTableNode", "cross_ref": "httpcore._synchronization.Lock", "kind": "Gdef"}, "NetworkBackend": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.base.NetworkBackend", "kind": "Gdef"}, "NetworkStream": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.base.NetworkStream", "kind": "Gdef"}, "Origin": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.Origin", "kind": "Gdef"}, "ProxyError": {".class": "SymbolTableNode", "cross_ref": "httpcore._exceptions.ProxyError", "kind": "Gdef"}, "REPLY_CODES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "httpcore._sync.socks_proxy.REPLY_CODES", "name": "REPLY_CODES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.bytes", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "Request": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.Request", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.Response", "kind": "Gdef"}, "SOCKSProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpcore._sync.connection_pool.ConnectionPool"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._sync.socks_proxy.SOCKSProxy", "name": "SOCKSProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "httpcore._sync.socks_proxy.SOCKSProxy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._sync.socks_proxy", "mro": ["httpcore._sync.socks_proxy.SOCKSProxy", "httpcore._sync.connection_pool.ConnectionPool", "httpcore._sync.interfaces.RequestInterface", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "proxy_url", "proxy_auth", "ssl_context", "max_connections", "max_keepalive_connections", "keepalive_expiry", "http1", "http2", "retries", "network_backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "httpcore._sync.socks_proxy.SOCKSProxy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "proxy_url", "proxy_auth", "ssl_context", "max_connections", "max_keepalive_connections", "keepalive_expiry", "http1", "http2", "retries", "network_backend"], "arg_types": ["httpcore._sync.socks_proxy.SOCKSProxy", {".class": "UnionType", "items": ["httpcore._models.URL", "builtins.bytes", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["httpcore._backends.base.NetworkBackend", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SOCKSProxy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_proxy_auth": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "httpcore._sync.socks_proxy.SOCKSProxy._proxy_auth", "name": "_proxy_auth", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_proxy_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._sync.socks_proxy.SOCKSProxy._proxy_url", "name": "_proxy_url", "setter_type": null, "type": "httpcore._models.URL"}}, "create_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "httpcore._sync.socks_proxy.SOCKSProxy.create_connection", "name": "create_connection", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "origin"], "arg_types": ["httpcore._sync.socks_proxy.SOCKSProxy", "httpcore._models.Origin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_connection of SOCKSProxy", "ret_type": "httpcore._sync.interfaces.ConnectionInterface", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._sync.socks_proxy.SOCKSProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._sync.socks_proxy.SOCKSProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Socks5Connection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpcore._sync.interfaces.ConnectionInterface"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._sync.socks_proxy.Socks5Connection", "name": "Socks5Connection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "httpcore._sync.socks_proxy.Socks5Connection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._sync.socks_proxy", "mro": ["httpcore._sync.socks_proxy.Socks5Connection", "httpcore._sync.interfaces.ConnectionInterface", "httpcore._sync.interfaces.RequestInterface", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "proxy_origin", "remote_origin", "proxy_auth", "ssl_context", "keepalive_expiry", "http1", "http2", "network_backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "httpcore._sync.socks_proxy.Socks5Connection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "proxy_origin", "remote_origin", "proxy_auth", "ssl_context", "keepalive_expiry", "http1", "http2", "network_backend"], "arg_types": ["httpcore._sync.socks_proxy.Socks5Connection", "httpcore._models.Origin", "httpcore._models.Origin", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["httpcore._backends.base.NetworkBackend", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Socks5Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "httpcore._sync.socks_proxy.Socks5Connection.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["httpcore._sync.socks_proxy.Socks5Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Socks5Connection", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_connect_failed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._sync.socks_proxy.Socks5Connection._connect_failed", "name": "_connect_failed", "setter_type": null, "type": "builtins.bool"}}, "_connect_lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._sync.socks_proxy.Socks5Connection._connect_lock", "name": "_connect_lock", "setter_type": null, "type": "httpcore._synchronization.Lock"}}, "_connection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "httpcore._sync.socks_proxy.Socks5Connection._connection", "name": "_connection", "setter_type": null, "type": {".class": "UnionType", "items": ["httpcore._sync.interfaces.ConnectionInterface", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_http1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._sync.socks_proxy.Socks5Connection._http1", "name": "_http1", "setter_type": null, "type": "builtins.bool"}}, "_http2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._sync.socks_proxy.Socks5Connection._http2", "name": "_http2", "setter_type": null, "type": "builtins.bool"}}, "_keepalive_expiry": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._sync.socks_proxy.Socks5Connection._keepalive_expiry", "name": "_keepalive_expiry", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_network_backend": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "httpcore._sync.socks_proxy.Socks5Connection._network_backend", "name": "_network_backend", "setter_type": null, "type": "httpcore._backends.base.NetworkBackend"}}, "_proxy_auth": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._sync.socks_proxy.Socks5Connection._proxy_auth", "name": "_proxy_auth", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_proxy_origin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._sync.socks_proxy.Socks5Connection._proxy_origin", "name": "_proxy_origin", "setter_type": null, "type": "httpcore._models.Origin"}}, "_remote_origin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._sync.socks_proxy.Socks5Connection._remote_origin", "name": "_remote_origin", "setter_type": null, "type": "httpcore._models.Origin"}}, "_ssl_context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._sync.socks_proxy.Socks5Connection._ssl_context", "name": "_ssl_context", "setter_type": null, "type": {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "can_handle_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "httpcore._sync.socks_proxy.Socks5Connection.can_handle_request", "name": "can_handle_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "origin"], "arg_types": ["httpcore._sync.socks_proxy.Socks5Connection", "httpcore._models.Origin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_handle_request of Socks5Connection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "httpcore._sync.socks_proxy.Socks5Connection.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._sync.socks_proxy.Socks5Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of Socks5Connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "httpcore._sync.socks_proxy.Socks5Connection.handle_request", "name": "handle_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["httpcore._sync.socks_proxy.Socks5Connection", "httpcore._models.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_request of Socks5Connection", "ret_type": "httpcore._models.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_expired": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "httpcore._sync.socks_proxy.Socks5Connection.has_expired", "name": "has_expired", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._sync.socks_proxy.Socks5Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_expired of Socks5Connection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "httpcore._sync.socks_proxy.Socks5Connection.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._sync.socks_proxy.Socks5Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info of Socks5Connection", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_available": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "httpcore._sync.socks_proxy.Socks5Connection.is_available", "name": "is_available", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._sync.socks_proxy.Socks5Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_available of Socks5Connection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "httpcore._sync.socks_proxy.Socks5Connection.is_closed", "name": "is_closed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._sync.socks_proxy.Socks5Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_closed of Socks5Connection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_idle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "httpcore._sync.socks_proxy.Socks5Connection.is_idle", "name": "is_idle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._sync.socks_proxy.Socks5Connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_idle of Socks5Connection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._sync.socks_proxy.Socks5Connection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._sync.socks_proxy.Socks5Connection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SyncBackend": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.sync.SyncBackend", "kind": "Gdef"}, "Trace": {".class": "SymbolTableNode", "cross_ref": "httpcore._trace.Trace", "kind": "Gdef"}, "URL": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.URL", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._sync.socks_proxy.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._sync.socks_proxy.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._sync.socks_proxy.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._sync.socks_proxy.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._sync.socks_proxy.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._sync.socks_proxy.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_init_socks5_connection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5], "arg_names": ["stream", "host", "port", "auth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "httpcore._sync.socks_proxy._init_socks5_connection", "name": "_init_socks5_connection", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5], "arg_names": ["stream", "host", "port", "auth"], "arg_types": ["httpcore._backends.base.NetworkStream", "builtins.bytes", "builtins.int", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_socks5_connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "default_ssl_context": {".class": "SymbolTableNode", "cross_ref": "httpcore._ssl.default_ssl_context", "kind": "Gdef"}, "enforce_bytes": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.enforce_bytes", "kind": "Gdef"}, "enforce_url": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.enforce_url", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "httpcore._sync.socks_proxy.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "socksio": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "httpcore._sync.socks_proxy.socksio", "name": "<PERSON><PERSON>", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "httpcore._sync.socks_proxy.socksio", "source_any": null, "type_of_any": 3}}}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpcore\\_sync\\socks_proxy.py"}