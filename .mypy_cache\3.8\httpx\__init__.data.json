{".class": "MypyFile", "_fullname": "httpx", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ASGITransport": {".class": "SymbolTableNode", "cross_ref": "httpx._transports.asgi.ASGITransport", "kind": "Gdef"}, "AsyncBaseTransport": {".class": "SymbolTableNode", "cross_ref": "httpx._transports.base.AsyncBaseTransport", "kind": "Gdef"}, "AsyncByteStream": {".class": "SymbolTableNode", "cross_ref": "httpx._types.AsyncByteStream", "kind": "Gdef"}, "AsyncClient": {".class": "SymbolTableNode", "cross_ref": "httpx._client.AsyncClient", "kind": "Gdef"}, "AsyncHTTPTransport": {".class": "SymbolTableNode", "cross_ref": "httpx._transports.default.AsyncHTTPTransport", "kind": "Gdef"}, "Auth": {".class": "SymbolTableNode", "cross_ref": "httpx._auth.Auth", "kind": "Gdef"}, "BaseTransport": {".class": "SymbolTableNode", "cross_ref": "httpx._transports.base.BaseTransport", "kind": "Gdef"}, "BasicAuth": {".class": "SymbolTableNode", "cross_ref": "httpx._auth.BasicAuth", "kind": "Gdef"}, "ByteStream": {".class": "SymbolTableNode", "cross_ref": "httpx._content.ByteStream", "kind": "Gdef"}, "Client": {".class": "SymbolTableNode", "cross_ref": "httpx._client.Client", "kind": "Gdef"}, "CloseError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.CloseError", "kind": "Gdef"}, "ConnectError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.ConnectError", "kind": "Gdef"}, "ConnectTimeout": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.ConnectTimeout", "kind": "Gdef"}, "CookieConflict": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.CookieConflict", "kind": "Gdef"}, "Cookies": {".class": "SymbolTableNode", "cross_ref": "httpx._models.Cookies", "kind": "Gdef"}, "DecodingError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.DecodingError", "kind": "Gdef"}, "DigestAuth": {".class": "SymbolTableNode", "cross_ref": "httpx._auth.DigestAuth", "kind": "Gdef"}, "HTTPError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.HTTPError", "kind": "Gdef"}, "HTTPStatusError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.HTTPStatusError", "kind": "Gdef"}, "HTTPTransport": {".class": "SymbolTableNode", "cross_ref": "httpx._transports.default.HTTPTransport", "kind": "Gdef"}, "Headers": {".class": "SymbolTableNode", "cross_ref": "httpx._models.Headers", "kind": "Gdef"}, "InvalidURL": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.InvalidURL", "kind": "Gdef"}, "Limits": {".class": "SymbolTableNode", "cross_ref": "httpx._config.Limits", "kind": "Gdef"}, "LocalProtocolError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.LocalProtocolError", "kind": "Gdef"}, "MockTransport": {".class": "SymbolTableNode", "cross_ref": "httpx._transports.mock.MockTransport", "kind": "Gdef"}, "NetRCAuth": {".class": "SymbolTableNode", "cross_ref": "httpx._auth.NetRCAuth", "kind": "Gdef"}, "NetworkError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.NetworkError", "kind": "Gdef"}, "PoolTimeout": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.PoolTimeout", "kind": "Gdef"}, "ProtocolError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.ProtocolError", "kind": "Gdef"}, "Proxy": {".class": "SymbolTableNode", "cross_ref": "httpx._config.Proxy", "kind": "Gdef"}, "ProxyError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.ProxyError", "kind": "Gdef"}, "QueryParams": {".class": "SymbolTableNode", "cross_ref": "httpx._urls.QueryParams", "kind": "Gdef"}, "ReadError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.ReadError", "kind": "Gdef"}, "ReadTimeout": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.ReadTimeout", "kind": "Gdef"}, "RemoteProtocolError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.RemoteProtocolError", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "httpx._models.Request", "kind": "Gdef"}, "RequestError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.RequestError", "kind": "Gdef"}, "RequestNotRead": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.RequestNotRead", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "httpx._models.Response", "kind": "Gdef"}, "ResponseNotRead": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.ResponseNotRead", "kind": "Gdef"}, "StreamClosed": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.StreamClosed", "kind": "Gdef"}, "StreamConsumed": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.StreamConsumed", "kind": "Gdef"}, "StreamError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.StreamError", "kind": "Gdef"}, "SyncByteStream": {".class": "SymbolTableNode", "cross_ref": "httpx._types.SyncByteStream", "kind": "Gdef"}, "Timeout": {".class": "SymbolTableNode", "cross_ref": "httpx._config.Timeout", "kind": "Gdef"}, "TimeoutException": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.TimeoutException", "kind": "Gdef"}, "TooManyRedirects": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.TooManyRedirects", "kind": "Gdef"}, "TransportError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.TransportError", "kind": "Gdef"}, "URL": {".class": "SymbolTableNode", "cross_ref": "httpx._urls.URL", "kind": "Gdef"}, "USE_CLIENT_DEFAULT": {".class": "SymbolTableNode", "cross_ref": "httpx._client.USE_CLIENT_DEFAULT", "kind": "Gdef"}, "UnsupportedProtocol": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.UnsupportedProtocol", "kind": "Gdef"}, "WSGITransport": {".class": "SymbolTableNode", "cross_ref": "httpx._transports.wsgi.WSGITransport", "kind": "Gdef"}, "WriteError": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.WriteError", "kind": "Gdef"}, "WriteTimeout": {".class": "SymbolTableNode", "cross_ref": "httpx._exceptions.WriteTimeout", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "httpx.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__description__": {".class": "SymbolTableNode", "cross_ref": "httpx.__version__.__description__", "kind": "Gdef"}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__locals": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "httpx.__locals", "name": "__locals", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__name": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_index_var", "is_inferred"], "fullname": "httpx.__name", "name": "__name", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__title__": {".class": "SymbolTableNode", "cross_ref": "httpx.__version__.__title__", "kind": "Gdef"}, "__version__": {".class": "SymbolTableNode", "cross_ref": "httpx.__version__.__version__", "kind": "Gdef"}, "codes": {".class": "SymbolTableNode", "cross_ref": "httpx._status_codes.codes", "kind": "Gdef"}, "create_ssl_context": {".class": "SymbolTableNode", "cross_ref": "httpx._config.create_ssl_context", "kind": "Gdef"}, "delete": {".class": "SymbolTableNode", "cross_ref": "httpx._api.delete", "kind": "Gdef"}, "get": {".class": "SymbolTableNode", "cross_ref": "httpx._api.get", "kind": "Gdef"}, "head": {".class": "SymbolTableNode", "cross_ref": "httpx._api.head", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "cross_ref": "httpx._main.main", "kind": "Gdef"}, "options": {".class": "SymbolTableNode", "cross_ref": "httpx._api.options", "kind": "Gdef"}, "patch": {".class": "SymbolTableNode", "cross_ref": "httpx._api.patch", "kind": "Gdef"}, "post": {".class": "SymbolTableNode", "cross_ref": "httpx._api.post", "kind": "Gdef"}, "put": {".class": "SymbolTableNode", "cross_ref": "httpx._api.put", "kind": "Gdef"}, "request": {".class": "SymbolTableNode", "cross_ref": "httpx._api.request", "kind": "Gdef"}, "stream": {".class": "SymbolTableNode", "cross_ref": "httpx._api.stream", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\httpx\\__init__.py"}