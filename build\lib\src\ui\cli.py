"""
Main CLI interface for Arien AI.

This module provides the interactive command-line interface with streaming
responses, user confirmations, and comprehensive error handling.
"""

import asyncio
import logging
import signal
import sys
from typing import Optional, List

import click
from rich.console import Console

from src.core.agent import ArienAgent
from src.core.exceptions import ArienError, ConfigurationError
from src.config.settings import get_settings, Settings
from src.ui.animation import BallAni<PERSON>
from src.ui.formatter import OutputFormatter

logger = logging.getLogger(__name__)


class CLI:
    """
    Main CLI interface for Arien AI.
    
    Handles user interaction, command processing, and response display
    with support for streaming, confirmations, and error handling.
    """
    
    def __init__(self) -> None:
        """Initialize CLI."""
        self.settings = get_settings()
        self.formatter = OutputFormatter()
        self.agent: Optional[ArienAgent] = None
        self.running = True
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum: int, frame) -> None:
        """Handle shutdown signals."""
        self.running = False
        self.formatter.print("\n[yellow]Shutting down gracefully...[/yellow]")
        sys.exit(0)
    
    async def initialize(self) -> bool:
        """
        Initialize the CLI and agent.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            # Validate configuration
            errors = self.settings.validate()
            if errors:
                self.formatter.format_error(
                    f"Configuration errors:\n" + "\n".join(f"• {error}" for error in errors),
                    "Configuration Error"
                )
                return False
            
            # Initialize agent
            self.agent = ArienAgent(self.settings)
            
            # Test connection to LLM provider
            if not await self.agent.validate_connection():
                self.formatter.format_error(
                    f"Failed to connect to {self.settings.llm.provider} provider. "
                    "Please check your configuration and network connection.",
                    "Connection Error"
                )
                return False
            
            return True
            
        except Exception as e:
            self.formatter.format_error(f"Initialization failed: {str(e)}", "Initialization Error")
            return False
    
    async def run_interactive(self) -> None:
        """Run interactive CLI session."""
        # Display welcome message
        self._display_welcome()
        
        # Main interaction loop
        while self.running:
            try:
                # Get user input
                user_input = self.formatter.format_user_input("Enter your request (or 'quit' to exit)")
                
                if not user_input.strip():
                    continue
                
                # Check for exit commands
                if user_input.strip().lower() in ["quit", "exit", "q"]:
                    break
                
                # Check for special commands
                if user_input.strip().startswith("/"):
                    await self._handle_special_command(user_input.strip())
                    continue
                
                # Process user request
                await self._process_request(user_input)
                
            except KeyboardInterrupt:
                self.formatter.print("\n[yellow]Use 'quit' to exit gracefully.[/yellow]")
                continue
            except Exception as e:
                logger.error(f"Unexpected error in CLI loop: {e}")
                self.formatter.format_error(f"Unexpected error: {str(e)}")
        
        # Cleanup
        await self._cleanup()
    
    async def run_single_command(self, command: str) -> None:
        """
        Run a single command and exit.
        
        Args:
            command: Command to execute
        """
        try:
            await self._process_request(command)
        except Exception as e:
            logger.error(f"Error executing command: {e}")
            self.formatter.format_error(f"Command execution failed: {str(e)}")
        finally:
            await self._cleanup()
    
    async def _process_request(self, user_input: str) -> None:
        """
        Process user request with the AI agent.
        
        Args:
            user_input: User's input/request
        """
        if not self.agent:
            self.formatter.format_error("Agent not initialized")
            return
        
        try:
            # Show processing animation
            async with BallAnimation("Processing your request", speed=self.settings.ui.animation_speed):
                # Process request with agent
                response = await self.agent.process_request(user_input)
            
            # Display response
            self.formatter.format_ai_response(
                response.content,
                model=self.settings.llm.model
            )
            
            # Display tool results if any
            for tool_result in response.tool_results:
                self.formatter.format_tool_result(tool_result.tool_name, tool_result.result)
            
        except ArienError as e:
            self.formatter.format_error(str(e), "AI Error")
        except Exception as e:
            logger.error(f"Error processing request: {e}")
            self.formatter.format_error(f"Failed to process request: {str(e)}")
    
    async def _handle_special_command(self, command: str) -> None:
        """
        Handle special CLI commands.
        
        Args:
            command: Special command starting with /
        """
        command = command[1:]  # Remove leading /
        
        if command == "help":
            self._display_help()
        elif command == "config":
            self._display_config()
        elif command == "status":
            await self._display_status()
        elif command == "clear":
            self.formatter.clear()
        elif command.startswith("set "):
            await self._handle_set_command(command[4:])
        else:
            self.formatter.format_error(f"Unknown command: /{command}")
    
    async def _handle_set_command(self, args: str) -> None:
        """
        Handle configuration set commands.
        
        Args:
            args: Arguments for set command
        """
        try:
            parts = args.split("=", 1)
            if len(parts) != 2:
                self.formatter.format_error("Usage: /set key=value")
                return
            
            key, value = parts[0].strip(), parts[1].strip()
            
            # Handle specific configuration keys
            if key == "model":
                self.settings.llm.model = value
                self.formatter.format_success(f"Model set to: {value}")
            elif key == "temperature":
                try:
                    self.settings.llm.temperature = float(value)
                    self.formatter.format_success(f"Temperature set to: {value}")
                except ValueError:
                    self.formatter.format_error("Temperature must be a number")
            elif key == "max_tokens":
                try:
                    self.settings.llm.max_tokens = int(value)
                    self.formatter.format_success(f"Max tokens set to: {value}")
                except ValueError:
                    self.formatter.format_error("Max tokens must be an integer")
            else:
                self.formatter.format_error(f"Unknown configuration key: {key}")
                
        except Exception as e:
            self.formatter.format_error(f"Failed to set configuration: {str(e)}")
    
    def _display_welcome(self) -> None:
        """Display welcome message."""
        welcome_text = f"""
🤖 Welcome to Arien AI v{self.settings.to_dict().get('version', '1.0.0')}

A sophisticated AI-powered CLI terminal system with:
• Multiple LLM providers (Deepseek, Ollama)
• Intelligent function tools (Bash, Web Search)
• Real-time streaming responses
• Comprehensive error handling

Provider: {self.settings.llm.provider}
Model: {self.settings.llm.model}

Type your request or use these commands:
• /help - Show available commands
• /config - Show current configuration
• /status - Show system status
• /clear - Clear screen
• quit - Exit

Let's get started! 🚀
"""
        self.formatter.format_info(welcome_text.strip(), "Welcome")
    
    def _display_help(self) -> None:
        """Display help information."""
        help_text = """
Available Commands:
• /help - Show this help message
• /config - Display current configuration
• /status - Show system and connection status
• /clear - Clear the screen
• /set key=value - Update configuration
  - /set model=deepseek-chat
  - /set temperature=0.7
  - /set max_tokens=4096
• quit, exit, q - Exit the application

Function Tools Available:
• Bash Tool - Execute shell commands safely
• Web Search Tool - Search the web for real-time information

Tips:
• Be specific in your requests for better results
• The AI can use tools automatically when needed
• Dangerous operations will ask for confirmation
• Use Ctrl+C to interrupt long-running operations
"""
        self.formatter.format_info(help_text.strip(), "Help")
    
    def _display_config(self) -> None:
        """Display current configuration."""
        config = self.settings.to_dict()
        
        config_text = f"""
LLM Configuration:
• Provider: {config['llm']['provider']}
• Model: {config['llm']['model']}
• Max Tokens: {config['llm']['max_tokens']}
• Temperature: {config['llm']['temperature']}
• Timeout: {config['llm']['timeout']}s

Tools Configuration:
• Bash Enabled: {config['tools']['bash_enabled']}
• Web Search Enabled: {config['tools']['web_search_enabled']}
• Bash Timeout: {config['tools']['bash_timeout']}s
• Web Search Max Results: {config['tools']['web_search_max_results']}

UI Configuration:
• Show Progress: {config['ui']['show_progress']}
• Show Timestamps: {config['ui']['show_timestamps']}
• Confirm Destructive: {config['ui']['confirm_destructive']}

System:
• Log Level: {config['log_level']}
"""
        self.formatter.format_info(config_text.strip(), "Configuration")
    
    async def _display_status(self) -> None:
        """Display system status."""
        if not self.agent:
            self.formatter.format_error("Agent not initialized")
            return
        
        try:
            # Check LLM connection
            llm_status = "✅ Connected" if await self.agent.validate_connection() else "❌ Disconnected"
            
            # Check tool availability
            bash_status = "✅ Available" if self.settings.tools.bash_enabled else "❌ Disabled"
            web_search_status = "✅ Available" if self.settings.tools.web_search_enabled else "❌ Disabled"
            
            status_text = f"""
System Status:
• LLM Provider ({self.settings.llm.provider}): {llm_status}
• Bash Tool: {bash_status}
• Web Search Tool: {web_search_status}

Connection Details:
• Model: {self.settings.llm.model}
• Base URL: {getattr(self.settings.llm, 'base_url', 'Default')}
"""
            self.formatter.format_info(status_text.strip(), "Status")
            
        except Exception as e:
            self.formatter.format_error(f"Failed to check status: {str(e)}")
    
    async def _cleanup(self) -> None:
        """Cleanup resources."""
        if self.agent:
            await self.agent.cleanup()
        
        self.formatter.format_success("Goodbye! 👋", "Exit")
