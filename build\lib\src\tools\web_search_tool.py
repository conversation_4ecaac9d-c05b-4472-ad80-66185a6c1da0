"""
Web search tool using DuckDuckGo API.

This tool provides real-time web information retrieval with rate limiting,
caching, and intelligent result formatting for AI consumption.
"""

import asyncio
import hashlib
import json
import logging
import time
from typing import Dict, Any, List, Optional
from urllib.parse import quote_plus

import httpx

from src.tools.base import BaseTool, ToolResult, ToolStatus
from src.core.exceptions import (
    WebSearchError,
    WebSearchRateLimitError,
    WebSearchConnectionError,
    WebSearchInvalidQueryError,
)
from src.core.retry import retry_on_failure
from src.config.settings import get_settings

logger = logging.getLogger(__name__)


class WebSearchTool(BaseTool):
    """
    Web search tool using DuckDuckGo's free API.
    
    Features:
    - Real-time web information retrieval
    - Rate limiting and error handling
    - Result caching to minimize API calls
    - Intelligent result formatting
    - Support for different search types
    """
    
    def __init__(self, **kwargs) -> None:
        """Initialize web search tool."""
        super().__init__(**kwargs)
        self.settings = get_settings()
        
        # Simple in-memory cache
        self._cache: Dict[str, Dict[str, Any]] = {}
        
        # Rate limiting
        self._last_request_time = 0
        self._min_request_interval = 1.0  # Minimum 1 second between requests
        
        # HTTP client
        self.client = httpx.AsyncClient(
            headers={
                "User-Agent": "Arien-AI/1.0 (https://github.com/arien-ai/arien-ai)"
            },
            timeout=httpx.Timeout(30.0),
        )
    
    @property
    def name(self) -> str:
        """Tool name."""
        return "web_search"
    
    @property
    def description(self) -> str:
        """Tool description for LLM."""
        return """Search the web for real-time information using DuckDuckGo.

WHEN TO USE:
- Finding current information not in training data
- Researching recent events, news, or developments
- Looking up specific facts, statistics, or data
- Finding documentation, tutorials, or guides
- Checking current prices, availability, or status
- Researching companies, products, or services
- Getting multiple perspectives on topics

WHEN NOT TO USE:
- For information you already know from training data
- For simple calculations or basic programming questions
- For personal or private information
- For illegal or harmful content searches

SEARCH STRATEGY:
- Use specific, targeted queries for better results
- Include relevant keywords and context
- Try different phrasings if initial results aren't helpful
- Combine with other tools for comprehensive analysis

RESULT PROCESSING:
- Results are automatically formatted and summarized
- Multiple sources provide diverse perspectives
- Recent results are prioritized when available
- Cached results reduce API calls for repeated queries

EXAMPLES:
- "latest Python 3.12 features"
- "current Bitcoin price USD"
- "how to deploy Docker containers AWS"
- "recent developments artificial intelligence 2024"
- "best practices React hooks 2024"
"""
    
    @property
    def parameters(self) -> Dict[str, Any]:
        """Tool parameters schema."""
        return {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Search query string"
                },
                "max_results": {
                    "type": "integer",
                    "description": "Maximum number of results to return (default: 5, max: 10)",
                    "minimum": 1,
                    "maximum": 10
                },
                "region": {
                    "type": "string",
                    "description": "Search region (e.g., 'us-en', 'uk-en', 'de-de')",
                    "default": "us-en"
                },
                "time_range": {
                    "type": "string",
                    "description": "Time range for results ('d' for day, 'w' for week, 'm' for month, 'y' for year)",
                    "enum": ["d", "w", "m", "y"]
                }
            },
            "required": ["query"]
        }
    
    @property
    def can_run_parallel(self) -> bool:
        """Web searches can run in parallel."""
        return True
    
    @property
    def is_destructive(self) -> bool:
        """Web search is not destructive."""
        return False
    
    def _get_cache_key(self, query: str, **kwargs) -> str:
        """Generate cache key for query."""
        # Include relevant parameters in cache key
        cache_data = {
            "query": query,
            "max_results": kwargs.get("max_results", 5),
            "region": kwargs.get("region", "us-en"),
            "time_range": kwargs.get("time_range"),
        }
        cache_string = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_string.encode()).hexdigest()
    
    def _is_cache_valid(self, cache_entry: Dict[str, Any]) -> bool:
        """Check if cache entry is still valid."""
        cache_time = cache_entry.get("timestamp", 0)
        cache_ttl = self.settings.tools.web_search_cache_ttl
        return time.time() - cache_time < cache_ttl
    
    async def _rate_limit(self) -> None:
        """Implement rate limiting."""
        current_time = time.time()
        time_since_last = current_time - self._last_request_time
        
        if time_since_last < self._min_request_interval:
            sleep_time = self._min_request_interval - time_since_last
            await asyncio.sleep(sleep_time)
        
        self._last_request_time = time.time()
    
    @retry_on_failure(max_retries=3)
    async def _search_duckduckgo(
        self,
        query: str,
        max_results: int = 5,
        region: str = "us-en",
        time_range: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Search using DuckDuckGo's instant answer API.
        
        Args:
            query: Search query
            max_results: Maximum results to return
            region: Search region
            time_range: Time range filter
            
        Returns:
            List of search results
        """
        try:
            # Rate limiting
            await self._rate_limit()
            
            # Prepare search URL
            base_url = "https://api.duckduckgo.com/"
            params = {
                "q": query,
                "format": "json",
                "no_html": "1",
                "skip_disambig": "1",
            }
            
            logger.debug(f"Searching DuckDuckGo for: {query}")
            
            # Make request
            response = await self.client.get(base_url, params=params)
            
            if response.status_code == 429:
                raise WebSearchRateLimitError("DuckDuckGo rate limit exceeded")
            elif response.status_code != 200:
                raise WebSearchConnectionError(f"DuckDuckGo API error: {response.status_code}")
            
            data = response.json()
            
            # Extract results
            results = []
            
            # Add instant answer if available
            if data.get("Abstract"):
                results.append({
                    "title": data.get("Heading", "Instant Answer"),
                    "snippet": data["Abstract"],
                    "url": data.get("AbstractURL", ""),
                    "source": data.get("AbstractSource", "DuckDuckGo"),
                    "type": "instant_answer"
                })
            
            # Add related topics
            for topic in data.get("RelatedTopics", [])[:max_results]:
                if isinstance(topic, dict) and "Text" in topic:
                    results.append({
                        "title": topic.get("FirstURL", "").split("/")[-1].replace("_", " "),
                        "snippet": topic["Text"],
                        "url": topic.get("FirstURL", ""),
                        "source": "Wikipedia",
                        "type": "related_topic"
                    })
            
            # If we don't have enough results, try the web search API
            if len(results) < max_results:
                await self._search_web_fallback(query, max_results - len(results), results)
            
            return results[:max_results]
            
        except httpx.TimeoutException:
            raise WebSearchConnectionError("DuckDuckGo search timed out")
        except httpx.ConnectError:
            raise WebSearchConnectionError("Failed to connect to DuckDuckGo")
        except Exception as e:
            if isinstance(e, (WebSearchRateLimitError, WebSearchConnectionError)):
                raise
            raise WebSearchError(f"Search failed: {str(e)}")
    
    async def _search_web_fallback(
        self,
        query: str,
        max_results: int,
        existing_results: List[Dict[str, Any]]
    ) -> None:
        """
        Fallback web search using alternative methods.
        
        This is a simplified implementation. In a production system,
        you might integrate with other search APIs or web scraping.
        """
        try:
            # Use a simple web search approach
            # This is a placeholder - you could integrate with other APIs
            search_url = f"https://html.duckduckgo.com/html/?q={quote_plus(query)}"
            
            # Note: This is a simplified approach
            # In practice, you'd need proper HTML parsing and result extraction
            logger.debug(f"Fallback search for: {query}")
            
        except Exception as e:
            logger.warning(f"Fallback search failed: {e}")
    
    def _format_results(self, results: List[Dict[str, Any]]) -> str:
        """Format search results for AI consumption."""
        if not results:
            return "No search results found."
        
        formatted = "Search Results:\n\n"
        
        for i, result in enumerate(results, 1):
            title = result.get("title", "No title")
            snippet = result.get("snippet", "No description available")
            url = result.get("url", "")
            source = result.get("source", "Unknown")
            
            formatted += f"{i}. **{title}**\n"
            formatted += f"   Source: {source}\n"
            if url:
                formatted += f"   URL: {url}\n"
            formatted += f"   {snippet}\n\n"
        
        return formatted.strip()
    
    async def execute(self, **kwargs) -> ToolResult:
        """
        Execute web search.
        
        Args:
            query: Search query
            max_results: Maximum results (optional)
            region: Search region (optional)
            time_range: Time range filter (optional)
            
        Returns:
            Tool execution result
        """
        start_time = time.time()
        
        try:
            # Validate parameters
            errors = self.validate_parameters(**kwargs)
            if errors:
                return ToolResult(
                    status=ToolStatus.ERROR,
                    output="",
                    error=f"Parameter validation failed: {', '.join(errors)}",
                    execution_time=time.time() - start_time
                )
            
            query = kwargs["query"].strip()
            if not query:
                return ToolResult(
                    status=ToolStatus.ERROR,
                    output="",
                    error="Search query cannot be empty",
                    execution_time=time.time() - start_time
                )
            
            max_results = min(kwargs.get("max_results", 5), self.settings.tools.web_search_max_results)
            region = kwargs.get("region", "us-en")
            time_range = kwargs.get("time_range")
            
            logger.info(f"Searching web for: {query}")
            
            # Check cache first
            cache_key = self._get_cache_key(query, max_results=max_results, region=region, time_range=time_range)
            if cache_key in self._cache and self._is_cache_valid(self._cache[cache_key]):
                logger.debug("Using cached search results")
                cached_result = self._cache[cache_key]
                return ToolResult(
                    status=ToolStatus.SUCCESS,
                    output=cached_result["output"],
                    metadata={
                        **cached_result["metadata"],
                        "cached": True,
                    },
                    execution_time=time.time() - start_time
                )
            
            # Perform search
            results = await self._search_duckduckgo(
                query=query,
                max_results=max_results,
                region=region,
                time_range=time_range
            )
            
            # Format results
            formatted_output = self._format_results(results)
            
            # Cache results
            cache_entry = {
                "timestamp": time.time(),
                "output": formatted_output,
                "metadata": {
                    "query": query,
                    "results_count": len(results),
                    "region": region,
                    "time_range": time_range,
                }
            }
            self._cache[cache_key] = cache_entry
            
            return ToolResult(
                status=ToolStatus.SUCCESS,
                output=formatted_output,
                metadata={
                    "query": query,
                    "results_count": len(results),
                    "region": region,
                    "time_range": time_range,
                    "cached": False,
                },
                execution_time=time.time() - start_time
            )
            
        except WebSearchInvalidQueryError as e:
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Invalid search query: {str(e)}",
                execution_time=time.time() - start_time
            )
        except (WebSearchRateLimitError, WebSearchConnectionError) as e:
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Search service error: {str(e)}",
                execution_time=time.time() - start_time
            )
        except Exception as e:
            logger.error(f"Web search error: {e}")
            return ToolResult(
                status=ToolStatus.ERROR,
                output="",
                error=f"Search failed: {str(e)}",
                execution_time=time.time() - start_time
            )
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.client.aclose()
