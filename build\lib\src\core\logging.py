"""
Advanced logging configuration for Arien AI.

This module provides comprehensive logging setup with file rotation,
structured logging, and performance monitoring.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

from rich.logging import <PERSON>Handler
from rich.console import Console

from src.config.settings import get_settings


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record with structured information."""
        # Add custom fields
        record.timestamp = datetime.utcnow().isoformat()
        record.module_name = record.module
        record.function_name = record.funcName
        
        # Add context if available
        if hasattr(record, 'user_id'):
            record.user_context = f"user:{record.user_id}"
        else:
            record.user_context = "system"
        
        if hasattr(record, 'request_id'):
            record.request_context = f"req:{record.request_id}"
        else:
            record.request_context = "none"
        
        return super().format(record)


class PerformanceLogger:
    """Logger for performance monitoring and metrics."""
    
    def __init__(self, name: str = "arien.performance"):
        self.logger = logging.getLogger(name)
        self.metrics: Dict[str, Any] = {}
    
    def log_execution_time(self, operation: str, duration: float, **kwargs) -> None:
        """Log execution time for operations."""
        self.logger.info(
            f"Performance: {operation} completed in {duration:.3f}s",
            extra={
                "operation": operation,
                "duration": duration,
                "performance_metric": True,
                **kwargs
            }
        )
        
        # Store metrics for analysis
        if operation not in self.metrics:
            self.metrics[operation] = []
        self.metrics[operation].append({
            "duration": duration,
            "timestamp": datetime.utcnow(),
            **kwargs
        })
    
    def log_tool_usage(self, tool_name: str, success: bool, duration: float, **kwargs) -> None:
        """Log tool usage statistics."""
        status = "success" if success else "failure"
        self.logger.info(
            f"Tool: {tool_name} {status} in {duration:.3f}s",
            extra={
                "tool_name": tool_name,
                "success": success,
                "duration": duration,
                "tool_usage": True,
                **kwargs
            }
        )
    
    def log_llm_usage(self, provider: str, model: str, tokens: int, duration: float, **kwargs) -> None:
        """Log LLM usage statistics."""
        self.logger.info(
            f"LLM: {provider}/{model} used {tokens} tokens in {duration:.3f}s",
            extra={
                "provider": provider,
                "model": model,
                "tokens": tokens,
                "duration": duration,
                "llm_usage": True,
                **kwargs
            }
        )
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of collected metrics."""
        summary = {}
        for operation, measurements in self.metrics.items():
            if measurements:
                durations = [m["duration"] for m in measurements]
                summary[operation] = {
                    "count": len(measurements),
                    "avg_duration": sum(durations) / len(durations),
                    "min_duration": min(durations),
                    "max_duration": max(durations),
                    "total_duration": sum(durations)
                }
        return summary


def setup_logging(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    enable_rich: bool = True,
    enable_performance: bool = True,
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
) -> None:
    """
    Setup comprehensive logging configuration.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        log_file: Optional log file path
        enable_rich: Enable rich console logging
        enable_performance: Enable performance logging
        max_file_size: Maximum log file size before rotation
        backup_count: Number of backup files to keep
    """
    # Clear existing handlers
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    
    # Set log level
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    handlers = []
    
    # Console handler with Rich formatting
    if enable_rich:
        console = Console(stderr=True)
        rich_handler = RichHandler(
            console=console,
            rich_tracebacks=True,
            tracebacks_show_locals=log_level.upper() == "DEBUG",
            markup=True
        )
        rich_handler.setLevel(getattr(logging, log_level.upper()))
        handlers.append(rich_handler)
    else:
        # Standard console handler
        console_handler = logging.StreamHandler(sys.stderr)
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        handlers.append(console_handler)
    
    # File handler with rotation
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        
        # Structured formatter for file logs
        file_formatter = StructuredFormatter(
            '%(timestamp)s | %(levelname)-8s | %(user_context)-10s | '
            '%(request_context)-10s | %(name)-20s | %(module_name)-15s | '
            '%(function_name)-15s | %(lineno)-4d | %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        file_handler.setLevel(logging.DEBUG)  # Always log everything to file
        handlers.append(file_handler)
    
    # Add handlers to root logger
    for handler in handlers:
        root_logger.addHandler(handler)
    
    # Configure specific loggers
    
    # Reduce noise from external libraries
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    
    # Arien AI specific loggers
    arien_logger = logging.getLogger("arien")
    arien_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Performance logger
    if enable_performance:
        perf_logger = logging.getLogger("arien.performance")
        perf_logger.setLevel(logging.INFO)
    
    # Security logger for audit trail
    security_logger = logging.getLogger("arien.security")
    security_logger.setLevel(logging.INFO)
    
    # Tool execution logger
    tools_logger = logging.getLogger("arien.tools")
    tools_logger.setLevel(logging.INFO)


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the specified name.
    
    Args:
        name: Logger name
        
    Returns:
        Configured logger instance
    """
    return logging.getLogger(f"arien.{name}")


def log_security_event(event_type: str, details: Dict[str, Any], user_id: Optional[str] = None) -> None:
    """
    Log security-related events for audit trail.
    
    Args:
        event_type: Type of security event
        details: Event details
        user_id: Optional user identifier
    """
    security_logger = logging.getLogger("arien.security")
    security_logger.warning(
        f"Security event: {event_type}",
        extra={
            "event_type": event_type,
            "user_id": user_id or "unknown",
            "security_event": True,
            **details
        }
    )


def log_tool_execution(
    tool_name: str,
    command: str,
    success: bool,
    duration: float,
    user_id: Optional[str] = None,
    **kwargs
) -> None:
    """
    Log tool execution for audit and monitoring.
    
    Args:
        tool_name: Name of the tool
        command: Command or operation executed
        success: Whether execution was successful
        duration: Execution duration in seconds
        user_id: Optional user identifier
        **kwargs: Additional context
    """
    tools_logger = logging.getLogger("arien.tools")
    level = logging.INFO if success else logging.WARNING
    
    tools_logger.log(
        level,
        f"Tool execution: {tool_name} - {command}",
        extra={
            "tool_name": tool_name,
            "command": command,
            "success": success,
            "duration": duration,
            "user_id": user_id or "unknown",
            "tool_execution": True,
            **kwargs
        }
    )


# Global performance logger instance
performance_logger = PerformanceLogger()


def setup_default_logging() -> None:
    """Setup default logging configuration from settings."""
    settings = get_settings()
    
    setup_logging(
        log_level=settings.log_level,
        log_file=settings.log_file,
        enable_rich=settings.ui.color_output,
        enable_performance=True
    )
