<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">42%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-08 17:24 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html">src\__init__.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997___init___py.html">src\config\__init__.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t26">src\config\profiles.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t26"><data value='init__'>ProfileManager.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t30">src\config\profiles.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t30"><data value='load_builtin_profiles'>ProfileManager._load_builtin_profiles</data></a></td>
                <td>60</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="60 60">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t157">src\config\profiles.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t157"><data value='get_profile'>ProfileManager.get_profile</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t169">src\config\profiles.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t169"><data value='list_profiles'>ProfileManager.list_profiles</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t178">src\config\profiles.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t178"><data value='find_profiles_by_tag'>ProfileManager.find_profiles_by_tag</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t193">src\config\profiles.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t193"><data value='create_custom_profile'>ProfileManager.create_custom_profile</data></a></td>
                <td>18</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="16 18">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t249">src\config\profiles.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t249"><data value='save_profile'>ProfileManager.save_profile</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t269">src\config\profiles.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t269"><data value='load_profile'>ProfileManager.load_profile</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t308">src\config\profiles.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t308"><data value='get_profile_recommendations'>ProfileManager.get_profile_recommendations</data></a></td>
                <td>19</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="14 19">74%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t354">src\config\profiles.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html#t354"><data value='get_profile_manager'>get_profile_manager</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html">src\config\profiles.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_profiles_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t72">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t72"><data value='post_init__'>Settings.__post_init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t77">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t77"><data value='load_environment'>Settings._load_environment</data></a></td>
                <td>25</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="20 25">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t117">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t117"><data value='ensure_config_dir'>Settings._ensure_config_dir</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t121">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t121"><data value='get_api_key'>Settings.get_api_key</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t129">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t129"><data value='validate'>Settings.validate</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t155">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t155"><data value='to_dict'>Settings.to_dict</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t189">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t189"><data value='get_settings'>get_settings</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t197">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t197"><data value='reset_settings'>reset_settings</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t203">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t203"><data value='apply_config_dict'>apply_config_dict</data></a></td>
                <td>49</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="0 49">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t272">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html#t272"><data value='apply_profile_settings'>apply_profile_settings</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html">src\config\settings.py</a></td>
                <td class="name left"><a href="z_b695c9c33e1e1997_settings_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>56</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="56 56">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41___init___py.html">src\core\__init__.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t158">src\core\agent.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t158"><data value='init__'>ArienAgent.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t177">src\core\agent.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t177"><data value='create_llm_provider'>ArienAgent._create_llm_provider</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t202">src\core\agent.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t202"><data value='create_tools'>ArienAgent._create_tools</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t217">src\core\agent.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t217"><data value='get_tool_schemas'>ArienAgent._get_tool_schemas</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t221">src\core\agent.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t221"><data value='validate_connection'>ArienAgent.validate_connection</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t234">src\core\agent.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t234"><data value='process_request'>ArienAgent.process_request</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t335">src\core\agent.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t335"><data value='execute_tools'>ArienAgent._execute_tools</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t381">src\core\agent.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t381"><data value='analyze_execution_strategy'>ArienAgent._analyze_execution_strategy</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t433">src\core\agent.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t433"><data value='execute_single_tool'>ArienAgent._execute_single_tool</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t496">src\core\agent.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t496"><data value='process_tool_results'>ArienAgent._process_tool_results</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t519">src\core\agent.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html#t519"><data value='cleanup'>ArienAgent.cleanup</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html">src\core\agent.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_agent_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>39</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 39">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t50">src\core\context.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t50"><data value='init__'>ConversationContext.__init__</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t68">src\core\context.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t68"><data value='initialize_session'>ConversationContext._initialize_session</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t80">src\core\context.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t80"><data value='add_system_message'>ConversationContext.add_system_message</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t85">src\core\context.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t85"><data value='add_user_message'>ConversationContext.add_user_message</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t90">src\core\context.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t90"><data value='add_assistant_message'>ConversationContext.add_assistant_message</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t103">src\core\context.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t103"><data value='add_tool_message'>ConversationContext.add_tool_message</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t125">src\core\context.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t125"><data value='add_conversation_turn'>ConversationContext.add_conversation_turn</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t148">src\core\context.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t148"><data value='get_context_messages'>ConversationContext.get_context_messages</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t178">src\core\context.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t178"><data value='get_relevant_history'>ConversationContext.get_relevant_history</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t211">src\core\context.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t211"><data value='get_working_context'>ConversationContext.get_working_context</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t230">src\core\context.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t230"><data value='get_recent_tools'>ConversationContext._get_recent_tools</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t246">src\core\context.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t246"><data value='generate_context_summary'>ConversationContext.generate_context_summary</data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t287">src\core\context.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t287"><data value='save_session'>ConversationContext.save_session</data></a></td>
                <td>8</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="4 8">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t310">src\core\context.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t310"><data value='load_session'>ConversationContext.load_session</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t333">src\core\context.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t333"><data value='clear_context'>ConversationContext.clear_context</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t349">src\core\context.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html#t349"><data value='get_context_stats'>ConversationContext.get_context_stats</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html">src\core\context.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_context_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>42</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="42 42">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t14">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t14"><data value='init__'>ArienError.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t25">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t25"><data value='str__'>ArienError.__str__</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t96">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t96"><data value='init__'>BashExecutionError.__init__</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t137">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t137"><data value='classify_http_error'>classify_http_error</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t163">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html#t163"><data value='is_retryable_error'>is_retryable_error</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html">src\core\exceptions.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>38</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="38 38">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t24">src\core\logging.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t24"><data value='format'>StructuredFormatter.format</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t48">src\core\logging.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t48"><data value='init__'>PerformanceLogger.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t52">src\core\logging.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t52"><data value='log_execution_time'>PerformanceLogger.log_execution_time</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t73">src\core\logging.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t73"><data value='log_tool_usage'>PerformanceLogger.log_tool_usage</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t87">src\core\logging.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t87"><data value='log_llm_usage'>PerformanceLogger.log_llm_usage</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t101">src\core\logging.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t101"><data value='get_metrics_summary'>PerformanceLogger.get_metrics_summary</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t117">src\core\logging.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t117"><data value='setup_logging'>setup_logging</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t217">src\core\logging.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t217"><data value='get_logger'>get_logger</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t230">src\core\logging.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t230"><data value='log_security_event'>log_security_event</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t251">src\core\logging.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t251"><data value='log_tool_execution'>log_tool_execution</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t292">src\core\logging.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html#t292"><data value='setup_default_logging'>setup_default_logging</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html">src\core\logging.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_logging_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_retry_py.html#t35">src\core\retry.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_retry_py.html#t35"><data value='init__'>RetryManager.__init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_retry_py.html#t61">src\core\retry.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_retry_py.html#t61"><data value='calculate_delay'>RetryManager.calculate_delay</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_retry_py.html#t85">src\core\retry.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_retry_py.html#t85"><data value='retry_async'>RetryManager.retry_async</data></a></td>
                <td>22</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="18 22">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_retry_py.html#t145">src\core\retry.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_retry_py.html#t145"><data value='retry_sync'>RetryManager.retry_sync</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_retry_py.html#t206">src\core\retry.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_retry_py.html#t206"><data value='retry_on_failure'>retry_on_failure</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_retry_py.html#t223">src\core\retry.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_retry_py.html#t223"><data value='decorator'>retry_on_failure.decorator</data></a></td>
                <td>9</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="5 9">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_retry_py.html#t234">src\core\retry.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_retry_py.html#t234"><data value='async_wrapper'>retry_on_failure.decorator.async_wrapper</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce21df766c911d41_retry_py.html">src\core\retry.py</a></td>
                <td class="name left"><a href="z_ce21df766c911d41_retry_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t22">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t22"><data value='setup_logging'>setup_logging</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t77">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t77"><data value='cli'>cli</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t140">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t140"><data value='run'>run</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t150">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t150"><data value='config'>config</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t176">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t176"><data value='validate'>validate</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t192">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t192"><data value='test_connections'>validate.test_connections</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t221">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t221"><data value='version'>version</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t237">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t237"><data value='profile'>profile</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t243">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t243"><data value='list_profiles'>list_profiles</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t269">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t269"><data value='show_profile'>show_profile</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t302">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t302"><data value='recommend_profiles'>recommend_profiles</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t326">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t326"><data value='run_interactive'>run_interactive</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t345">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t345"><data value='run_single_command'>run_single_command</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t367">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html#t367"><data value='main'>main</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html">src\main.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>42</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 42">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60___init___py.html">src\providers\__init__.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t59">src\providers\base.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t59"><data value='init__'>BaseLLMProvider.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t64">src\providers\base.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t64"><data value='generate_response'>BaseLLMProvider.generate_response</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t84">src\providers\base.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t84"><data value='stream_response'>BaseLLMProvider.stream_response</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t104">src\providers\base.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t104"><data value='validate_connection'>BaseLLMProvider.validate_connection</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t113">src\providers\base.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t113"><data value='format_messages'>BaseLLMProvider.format_messages</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t143">src\providers\base.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t143"><data value='parse_tool_calls'>BaseLLMProvider.parse_tool_calls</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t182">src\providers\base.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t182"><data value='name'>BaseLLMProvider.name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t188">src\providers\base.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t188"><data value='supports_streaming'>BaseLLMProvider.supports_streaming</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t194">src\providers\base.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html#t194"><data value='supports_tools'>BaseLLMProvider.supports_tools</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html">src\providers\base.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>48</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="48 48">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html#t36">src\providers\deepseek.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html#t36"><data value='init__'>DeepseekProvider.__init__</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html#t79">src\providers\deepseek.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html#t79"><data value='generate_response'>DeepseekProvider.generate_response</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html#t152">src\providers\deepseek.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html#t152"><data value='stream_response'>DeepseekProvider.stream_response</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html#t225">src\providers\deepseek.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html#t225"><data value='validate_connection'>DeepseekProvider.validate_connection</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html#t239">src\providers\deepseek.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html#t239"><data value='handle_http_error'>DeepseekProvider._handle_http_error</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html#t250">src\providers\deepseek.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html#t250"><data value='aenter__'>DeepseekProvider.__aenter__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html#t254">src\providers\deepseek.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html#t254"><data value='aexit__'>DeepseekProvider.__aexit__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html#t259">src\providers\deepseek.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html#t259"><data value='name'>DeepseekProvider.name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html#t264">src\providers\deepseek.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html#t264"><data value='supports_streaming'>DeepseekProvider.supports_streaming</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html#t269">src\providers\deepseek.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html#t269"><data value='supports_tools'>DeepseekProvider.supports_tools</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html">src\providers\deepseek.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_deepseek_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t33">src\providers\ollama.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t33"><data value='init__'>OllamaProvider.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t61">src\providers\ollama.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t61"><data value='generate_response'>OllamaProvider.generate_response</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t130">src\providers\ollama.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t130"><data value='stream_response'>OllamaProvider.stream_response</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t199">src\providers\ollama.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t199"><data value='validate_connection'>OllamaProvider.validate_connection</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t220">src\providers\ollama.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t220"><data value='try_parse_tool_calls_from_content'>OllamaProvider._try_parse_tool_calls_from_content</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t256">src\providers\ollama.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t256"><data value='handle_http_error'>OllamaProvider._handle_http_error</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t271">src\providers\ollama.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t271"><data value='aenter__'>OllamaProvider.__aenter__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t275">src\providers\ollama.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t275"><data value='aexit__'>OllamaProvider.__aexit__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t280">src\providers\ollama.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t280"><data value='name'>OllamaProvider.name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t285">src\providers\ollama.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t285"><data value='supports_streaming'>OllamaProvider.supports_streaming</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t290">src\providers\ollama.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html#t290"><data value='supports_tools'>OllamaProvider.supports_tools</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html">src\providers\ollama.py</a></td>
                <td class="name left"><a href="z_7b455957374e7e60_ollama_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40___init___py.html">src\tools\__init__.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t41">src\tools\base.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t41"><data value='init__'>BaseTool.__init__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t47">src\tools\base.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t47"><data value='name'>BaseTool.name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t53">src\tools\base.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t53"><data value='description'>BaseTool.description</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t59">src\tools\base.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t59"><data value='parameters'>BaseTool.parameters</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t64">src\tools\base.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t64"><data value='execute'>BaseTool.execute</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t77">src\tools\base.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t77"><data value='can_run_parallel'>BaseTool.can_run_parallel</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t87">src\tools\base.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t87"><data value='is_destructive'>BaseTool.is_destructive</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t97">src\tools\base.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t97"><data value='requires_confirmation'>BaseTool.requires_confirmation</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t106">src\tools\base.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t106"><data value='get_schema'>BaseTool.get_schema</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t122">src\tools\base.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t122"><data value='validate_parameters'>BaseTool.validate_parameters</data></a></td>
                <td>22</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="16 22">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t160">src\tools\base.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html#t160"><data value='call__'>BaseTool.__call__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html">src\tools\base.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>39</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 39">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html#t50">src\tools\bash_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html#t50"><data value='init__'>BashTool.__init__</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html#t62">src\tools\bash_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html#t62"><data value='name'>BashTool.name</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html#t67">src\tools\bash_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html#t67"><data value='description'>BashTool.description</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html#t106">src\tools\bash_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html#t106"><data value='parameters'>BashTool.parameters</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html#t132">src\tools\bash_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html#t132"><data value='can_run_parallel'>BashTool.can_run_parallel</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html#t137">src\tools\bash_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html#t137"><data value='is_destructive'>BashTool.is_destructive</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html#t141">src\tools\bash_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html#t141"><data value='is_dangerous_command'>BashTool._is_dangerous_command</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html#t154">src\tools\bash_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html#t154"><data value='is_system_modifying'>BashTool._is_system_modifying</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html#t159">src\tools\bash_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html#t159"><data value='analyze_command_safety'>BashTool._analyze_command_safety</data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html#t202">src\tools\bash_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html#t202"><data value='execute'>BashTool.execute</data></a></td>
                <td>39</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="33 39">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html">src\tools\bash_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_bash_tool_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t49">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t49"><data value='init__'>FileTool.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t55">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t55"><data value='name'>FileTool.name</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t60">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t60"><data value='description'>FileTool.description</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t106">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t106"><data value='parameters'>FileTool.parameters</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t153">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t153"><data value='can_run_parallel'>FileTool.can_run_parallel</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t158">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t158"><data value='is_destructive'>FileTool.is_destructive</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t162">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t162"><data value='validate_path'>FileTool._validate_path</data></a></td>
                <td>21</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="16 21">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t210">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t210"><data value='is_executable'>FileTool._is_executable</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t214">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t214"><data value='create_backup'>FileTool._create_backup</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t243">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t243"><data value='execute'>FileTool.execute</data></a></td>
                <td>16</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="15 16">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t307">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t307"><data value='handle_read'>FileTool._handle_read</data></a></td>
                <td>15</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="8 15">53%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t357">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t357"><data value='handle_write'>FileTool._handle_write</data></a></td>
                <td>13</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="10 13">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t394">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t394"><data value='handle_append'>FileTool._handle_append</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t426">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t426"><data value='handle_copy'>FileTool._handle_copy</data></a></td>
                <td>16</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="9 16">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t471">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t471"><data value='handle_move'>FileTool._handle_move</data></a></td>
                <td>14</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="10 14">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t512">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t512"><data value='handle_delete'>FileTool._handle_delete</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t560">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t560"><data value='handle_mkdir'>FileTool._handle_mkdir</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t580">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t580"><data value='handle_list'>FileTool._handle_list</data></a></td>
                <td>22</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="16 22">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t636">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t636"><data value='handle_exists'>FileTool._handle_exists</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t651">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t651"><data value='handle_info'>FileTool._handle_info</data></a></td>
                <td>15</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="12 15">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t696">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html#t696"><data value='handle_search'>FileTool._handle_search</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html">src\tools\file_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_file_tool_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>39</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 39">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t43">src\tools\web_search_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t43"><data value='init__'>WebSearchTool.__init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t64">src\tools\web_search_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t64"><data value='name'>WebSearchTool.name</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t69">src\tools\web_search_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t69"><data value='description'>WebSearchTool.description</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t109">src\tools\web_search_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t109"><data value='parameters'>WebSearchTool.parameters</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t139">src\tools\web_search_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t139"><data value='can_run_parallel'>WebSearchTool.can_run_parallel</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t144">src\tools\web_search_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t144"><data value='is_destructive'>WebSearchTool.is_destructive</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t148">src\tools\web_search_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t148"><data value='get_cache_key'>WebSearchTool._get_cache_key</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t160">src\tools\web_search_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t160"><data value='is_cache_valid'>WebSearchTool._is_cache_valid</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t166">src\tools\web_search_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t166"><data value='rate_limit'>WebSearchTool._rate_limit</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t178">src\tools\web_search_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t178"><data value='search_duckduckgo'>WebSearchTool._search_duckduckgo</data></a></td>
                <td>28</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="24 28">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t261">src\tools\web_search_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t261"><data value='search_web_fallback'>WebSearchTool._search_web_fallback</data></a></td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="3 5">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t285">src\tools\web_search_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t285"><data value='format_results'>WebSearchTool._format_results</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t306">src\tools\web_search_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t306"><data value='execute'>WebSearchTool.execute</data></a></td>
                <td>29</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="25 29">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t422">src\tools\web_search_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t422"><data value='aenter__'>WebSearchTool.__aenter__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t426">src\tools\web_search_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html#t426"><data value='aexit__'>WebSearchTool.__aexit__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html">src\tools\web_search_tool.py</a></td>
                <td class="name left"><a href="z_bef636d57f78fa40_web_search_tool_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9___init___py.html">src\ui\__init__.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t35">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t35"><data value='init__'>BallAnimation.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t49">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t49"><data value='format_elapsed_time'>BallAnimation._format_elapsed_time</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t55">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t55"><data value='animate'>BallAnimation._animate</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t76">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t76"><data value='start'>BallAnimation.start</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t83">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t83"><data value='stop'>BallAnimation.stop</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t99">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t99"><data value='aenter__'>BallAnimation.__aenter__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t104">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t104"><data value='aexit__'>BallAnimation.__aexit__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t114">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t114"><data value='init__'>ProgressIndicator.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t129">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t129"><data value='update'>ProgressIndicator.update</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t139">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t139"><data value='increment'>ProgressIndicator.increment</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t143">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t143"><data value='display'>ProgressIndicator._display</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t171">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t171"><data value='format_time'>ProgressIndicator._format_time</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t182">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t182"><data value='finish'>ProgressIndicator.finish</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t196">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t196"><data value='init__'>Spinner.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t209">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t209"><data value='animate'>Spinner._animate</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t220">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t220"><data value='start'>Spinner.start</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t226">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t226"><data value='stop'>Spinner.stop</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t242">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t242"><data value='aenter__'>Spinner.__aenter__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t247">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html#t247"><data value='aexit__'>Spinner.__aexit__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html">src\ui\animation.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_animation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t34">src\ui\cli.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t34"><data value='init__'>CLI.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t45">src\ui\cli.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t45"><data value='signal_handler'>CLI._signal_handler</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t51">src\ui\cli.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t51"><data value='initialize'>CLI.initialize</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t86">src\ui\cli.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t86"><data value='run_interactive'>CLI.run_interactive</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t122">src\ui\cli.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t122"><data value='run_single_command'>CLI.run_single_command</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t137">src\ui\cli.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t137"><data value='process_request'>CLI._process_request</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t170">src\ui\cli.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t170"><data value='handle_special_command'>CLI._handle_special_command</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t192">src\ui\cli.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t192"><data value='handle_set_command'>CLI._handle_set_command</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t229">src\ui\cli.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t229"><data value='display_welcome'>CLI._display_welcome</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t254">src\ui\cli.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t254"><data value='display_help'>CLI._display_help</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t280">src\ui\cli.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t280"><data value='display_config'>CLI._display_config</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t308">src\ui\cli.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t308"><data value='display_status'>CLI._display_status</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t337">src\ui\cli.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html#t337"><data value='cleanup'>CLI._cleanup</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html">src\ui\cli.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_cli_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t31">src\ui\formatter.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t31"><data value='init__'>OutputFormatter.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t38">src\ui\formatter.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t38"><data value='format_ai_response'>OutputFormatter.format_ai_response</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t70">src\ui\formatter.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t70"><data value='format_tool_result'>OutputFormatter.format_tool_result</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t134">src\ui\formatter.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t134"><data value='format_error'>OutputFormatter.format_error</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t152">src\ui\formatter.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t152"><data value='format_warning'>OutputFormatter.format_warning</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t170">src\ui\formatter.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t170"><data value='format_info'>OutputFormatter.format_info</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t188">src\ui\formatter.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t188"><data value='format_success'>OutputFormatter.format_success</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t206">src\ui\formatter.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t206"><data value='format_user_input'>OutputFormatter.format_user_input</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t218">src\ui\formatter.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t218"><data value='format_confirmation'>OutputFormatter.format_confirmation</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t237">src\ui\formatter.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t237"><data value='format_output_content'>OutputFormatter._format_output_content</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t267">src\ui\formatter.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t267"><data value='format_metadata'>OutputFormatter._format_metadata</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t289">src\ui\formatter.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t289"><data value='looks_like_json'>OutputFormatter._looks_like_json</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t295">src\ui\formatter.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t295"><data value='looks_like_xml'>OutputFormatter._looks_like_xml</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t300">src\ui\formatter.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t300"><data value='looks_like_code'>OutputFormatter._looks_like_code</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t314">src\ui\formatter.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t314"><data value='detect_language'>OutputFormatter._detect_language</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t337">src\ui\formatter.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t337"><data value='print'>OutputFormatter.print</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t341">src\ui\formatter.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html#t341"><data value='clear'>OutputFormatter.clear</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html">src\ui\formatter.py</a></td>
                <td class="name left"><a href="z_d8dae034b85f0cf9_formatter_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>2323</td>
                <td>1337</td>
                <td>0</td>
                <td class="right" data-ratio="986 2323">42%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-08 17:24 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
