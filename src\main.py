"""
Main entry point for Arien AI.

This module provides the command-line interface and application entry point
with support for both interactive and single-command modes.
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Optional

import click
from rich.logging import <PERSON><PERSON><PERSON><PERSON>

from src.config.settings import get_settings, reset_settings
from src.core.exceptions import ArienError, ConfigurationError
from src.ui.cli import CLI


def setup_logging(log_level: str, log_file: Optional[str] = None) -> None:
    """
    Setup logging configuration.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        log_file: Optional log file path
    """
    # Configure root logger
    handlers = [RichHandler(rich_tracebacks=True)]
    
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(
            logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        )
        handlers.append(file_handler)
    
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(message)s",
        datefmt="[%X]",
        handlers=handlers
    )
    
    # Reduce noise from external libraries
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)


@click.group(invoke_without_command=True)
@click.option(
    "--config-file",
    type=click.Path(exists=True),
    help="Path to configuration file"
)
@click.option(
    "--profile",
    type=str,
    help="Configuration profile to use"
)
@click.option(
    "--log-level",
    type=click.Choice(["DEBUG", "INFO", "WARNING", "ERROR"]),
    default="INFO",
    help="Logging level"
)
@click.option(
    "--log-file",
    type=click.Path(),
    help="Log file path"
)
@click.pass_context
def cli(ctx, config_file: Optional[str], profile: Optional[str], log_level: str, log_file: Optional[str]) -> None:
    """
    Arien AI - A sophisticated AI-powered CLI terminal system.
    
    Run without arguments for interactive mode, or use subcommands for specific operations.
    """
    # Setup logging
    setup_logging(log_level, log_file)

    # Load configuration
    if config_file:
        try:
            import json
            import yaml
            from pathlib import Path

            config_path = Path(config_file)
            if config_path.suffix.lower() in ['.json']:
                with open(config_path, 'r') as f:
                    config_data = json.load(f)
            elif config_path.suffix.lower() in ['.yaml', '.yml']:
                with open(config_path, 'r') as f:
                    config_data = yaml.safe_load(f)
            else:
                click.echo(f"Unsupported config file format: {config_path.suffix}", err=True)
                sys.exit(1)

            # Apply configuration
            from src.config.settings import apply_config_dict
            apply_config_dict(config_data)
            click.echo(f"Loaded configuration from: {config_file}")

        except Exception as e:
            click.echo(f"Failed to load config file: {e}", err=True)
            sys.exit(1)

    # Apply profile if specified
    if profile:
        from src.config.profiles import get_profile_manager
        profile_manager = get_profile_manager()
        profile_config = profile_manager.get_profile(profile)
        if profile_config:
            # Apply profile settings globally
            from src.config.settings import apply_profile_settings
            apply_profile_settings(profile_config.settings)
            click.echo(f"Using profile: {profile}")
        else:
            click.echo(f"Profile '{profile}' not found", err=True)
            sys.exit(1)

    # Store context for subcommands
    ctx.ensure_object(dict)
    ctx.obj['profile'] = profile
    ctx.obj['log_level'] = log_level
    ctx.obj['log_file'] = log_file

    # If no subcommand, run interactive mode
    if ctx.invoked_subcommand is None:
        asyncio.run(run_interactive())


@cli.command()
@click.argument("command", required=True)
def run(command: str) -> None:
    """
    Run a single command and exit.
    
    COMMAND: The command/request to execute
    """
    asyncio.run(run_single_command(command))


@cli.command()
def config() -> None:
    """Show current configuration."""
    try:
        settings = get_settings()
        config_dict = settings.to_dict()
        
        click.echo("Current Configuration:")
        click.echo("=" * 50)
        
        for section, values in config_dict.items():
            if isinstance(values, dict):
                click.echo(f"\n[{section.upper()}]")
                for key, value in values.items():
                    # Hide sensitive values
                    if "key" in key.lower() or "token" in key.lower():
                        value = "***" if value else "Not set"
                    click.echo(f"  {key}: {value}")
            else:
                click.echo(f"{section}: {values}")
                
    except Exception as e:
        click.echo(f"Error loading configuration: {e}", err=True)
        sys.exit(1)


@cli.command()
def validate() -> None:
    """Validate configuration and connections."""
    try:
        settings = get_settings()
        
        # Validate configuration
        errors = settings.validate()
        if errors:
            click.echo("Configuration Errors:", err=True)
            for error in errors:
                click.echo(f"  • {error}", err=True)
            sys.exit(1)
        
        click.echo("✅ Configuration is valid")
        
        # Test connections
        async def test_connections():
            from src.core.agent import ArienAgent
            
            try:
                agent = ArienAgent(settings)
                if await agent.validate_connection():
                    click.echo("✅ LLM provider connection successful")
                else:
                    click.echo("❌ LLM provider connection failed", err=True)
                    return False
                
                await agent.cleanup()
                return True
                
            except Exception as e:
                click.echo(f"❌ Connection test failed: {e}", err=True)
                return False
        
        if asyncio.run(test_connections()):
            click.echo("✅ All systems operational")
        else:
            sys.exit(1)
            
    except Exception as e:
        click.echo(f"Validation failed: {e}", err=True)
        sys.exit(1)


@cli.command()
def version() -> None:
    """Show version information."""
    try:
        from src import __version__
        click.echo(f"Arien AI v{__version__}")

        # Show additional info
        settings = get_settings()
        click.echo(f"Provider: {settings.llm.provider}")
        click.echo(f"Model: {settings.llm.model}")

    except Exception as e:
        click.echo(f"Error getting version: {e}", err=True)


@cli.group()
def profile() -> None:
    """Manage configuration profiles."""
    pass


@profile.command("list")
def list_profiles() -> None:
    """List available configuration profiles."""
    try:
        from src.config.profiles import get_profile_manager

        profile_manager = get_profile_manager()
        profiles = profile_manager.list_profiles()

        if not profiles:
            click.echo("No profiles available")
            return

        click.echo("Available Profiles:")
        click.echo("=" * 50)

        for name, prof in profiles.items():
            click.echo(f"\n{name}")
            click.echo(f"  Description: {prof.description}")
            click.echo(f"  Tags: {', '.join(prof.tags)}")

    except Exception as e:
        click.echo(f"Error listing profiles: {e}", err=True)


@profile.command("show")
@click.argument("profile_name")
def show_profile(profile_name: str) -> None:
    """Show detailed information about a profile."""
    try:
        from src.config.profiles import get_profile_manager

        profile_manager = get_profile_manager()
        prof = profile_manager.get_profile(profile_name)

        if not prof:
            click.echo(f"Profile '{profile_name}' not found", err=True)
            return

        click.echo(f"Profile: {prof.name}")
        click.echo(f"Description: {prof.description}")
        click.echo(f"Tags: {', '.join(prof.tags)}")
        click.echo("\nSettings:")
        click.echo("-" * 30)

        config_dict = prof.settings.to_dict()
        for section, values in config_dict.items():
            if isinstance(values, dict):
                click.echo(f"\n[{section.upper()}]")
                for key, value in values.items():
                    click.echo(f"  {key}: {value}")
            else:
                click.echo(f"{section}: {values}")

    except Exception as e:
        click.echo(f"Error showing profile: {e}", err=True)


@profile.command("recommend")
@click.argument("use_case")
def recommend_profiles(use_case: str) -> None:
    """Get profile recommendations for a use case."""
    try:
        from src.config.profiles import get_profile_manager

        profile_manager = get_profile_manager()
        recommendations = profile_manager.get_profile_recommendations(use_case)

        if not recommendations:
            click.echo("No recommendations found")
            return

        click.echo(f"Recommended profiles for '{use_case}':")
        click.echo("=" * 50)

        for prof in recommendations:
            click.echo(f"\n{prof.name}")
            click.echo(f"  {prof.description}")
            click.echo(f"  Tags: {', '.join(prof.tags)}")

    except Exception as e:
        click.echo(f"Error getting recommendations: {e}", err=True)


async def run_interactive() -> None:
    """Run interactive CLI session."""
    try:
        cli_interface = CLI()
        
        # Initialize
        if not await cli_interface.initialize():
            sys.exit(1)
        
        # Run interactive session
        await cli_interface.run_interactive()
        
    except KeyboardInterrupt:
        click.echo("\nGoodbye! 👋")
    except Exception as e:
        click.echo(f"Fatal error: {e}", err=True)
        sys.exit(1)


async def run_single_command(command: str) -> None:
    """
    Run a single command and exit.
    
    Args:
        command: Command to execute
    """
    try:
        cli_interface = CLI()
        
        # Initialize
        if not await cli_interface.initialize():
            sys.exit(1)
        
        # Run command
        await cli_interface.run_single_command(command)
        
    except Exception as e:
        click.echo(f"Command failed: {e}", err=True)
        sys.exit(1)


def main() -> None:
    """Main entry point."""
    try:
        cli()
    except KeyboardInterrupt:
        click.echo("\nGoodbye! 👋")
    except Exception as e:
        click.echo(f"Fatal error: {e}", err=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
