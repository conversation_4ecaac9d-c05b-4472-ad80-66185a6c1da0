"""
Ollama LLM provider implementation.

This module implements the Ollama API integration for local model support
with streaming capabilities.
"""

import json
import logging
from typing import List, Dict, Any, Optional, AsyncIterator

import httpx

from src.providers.base import BaseLLMProvider, LLMResponse, Message, ToolCall
from src.core.exceptions import (
    LLMConnectionError,
    LLMInvalidRequestError,
    LLMTimeoutError,
)
from src.core.retry import retry_on_failure

logger = logging.getLogger(__name__)


class OllamaProvider(BaseLLMProvider):
    """
    Ollama LLM provider implementation.
    
    Supports local Ollama models with streaming responses.
    Note: Function calling support depends on the specific model.
    """
    
    def __init__(
        self,
        model: str,
        base_url: str = "http://localhost:11434",
        timeout: int = 60,
        **kwargs
    ) -> None:
        """
        Initialize Ollama provider.
        
        Args:
            model: Model name (e.g., "llama2", "codellama", "mistral")
            base_url: Ollama server URL
            timeout: Request timeout in seconds
        """
        super().__init__(**kwargs)
        
        self.model = model
        self.base_url = base_url.rstrip("/")
        self.timeout = timeout
        
        # Create HTTP client
        self.client = httpx.AsyncClient(
            headers={"Content-Type": "application/json"},
            timeout=httpx.Timeout(timeout),
        )
    
    @retry_on_failure(max_retries=3)
    async def generate_response(
        self,
        messages: List[Message],
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> LLMResponse:
        """
        Generate a response from Ollama.
        
        Args:
            messages: List of conversation messages
            tools: Optional list of available tools (limited support)
            **kwargs: Additional parameters
            
        Returns:
            LLM response
        """
        try:
            # Prepare request payload
            payload = {
                "model": self.model,
                "messages": self.format_messages(messages),
                "stream": False,
                "options": {
                    "temperature": kwargs.get("temperature", 0.7),
                    "num_predict": kwargs.get("max_tokens", 4096),
                }
            }
            
            logger.debug(f"Sending request to Ollama: {self.model}")
            
            # Make API request
            response = await self.client.post(
                f"{self.base_url}/api/chat",
                json=payload,
            )
            
            # Handle HTTP errors
            if response.status_code != 200:
                await self._handle_http_error(response)
            
            # Parse response
            data = response.json()
            message = data.get("message", {})
            content = message.get("content", "")
            
            # Ollama doesn't have built-in tool calling, but we can try to parse
            # tool calls from the content if the model supports it
            tool_calls = []
            if tools and content:
                tool_calls = self._try_parse_tool_calls_from_content(content)
            
            return LLMResponse(
                content=content,
                tool_calls=tool_calls,
                finish_reason=data.get("done_reason", "stop"),
                usage=None,  # Ollama doesn't provide usage stats in the same format
                model=self.model,
            )
            
        except httpx.TimeoutException:
            raise LLMTimeoutError("Request to Ollama timed out")
        except httpx.ConnectError:
            raise LLMConnectionError("Failed to connect to Ollama server")
        except Exception as e:
            if isinstance(e, (LLMConnectionError, LLMInvalidRequestError, LLMTimeoutError)):
                raise
            raise LLMConnectionError(f"Unexpected error: {e}")
    
    async def stream_response(
        self,
        messages: List[Message],
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> AsyncIterator[str]:
        """
        Stream response from Ollama.
        
        Args:
            messages: List of conversation messages
            tools: Optional list of available tools
            **kwargs: Additional parameters
            
        Yields:
            Response chunks as they arrive
        """
        try:
            # Prepare request payload
            payload = {
                "model": self.model,
                "messages": self.format_messages(messages),
                "stream": True,
                "options": {
                    "temperature": kwargs.get("temperature", 0.7),
                    "num_predict": kwargs.get("max_tokens", 4096),
                }
            }
            
            logger.debug(f"Starting stream from Ollama: {self.model}")
            
            # Make streaming request
            async with self.client.stream(
                "POST",
                f"{self.base_url}/api/chat",
                json=payload,
            ) as response:
                
                # Handle HTTP errors
                if response.status_code != 200:
                    await self._handle_http_error(response)
                
                # Process streaming response
                async for line in response.aiter_lines():
                    if line.strip():
                        try:
                            data = json.loads(line)
                            message = data.get("message", {})
                            content = message.get("content", "")
                            
                            if content:
                                yield content
                            
                            # Check if done
                            if data.get("done", False):
                                break
                                
                        except json.JSONDecodeError:
                            continue
                            
        except httpx.TimeoutException:
            raise LLMTimeoutError("Stream from Ollama timed out")
        except httpx.ConnectError:
            raise LLMConnectionError("Failed to connect to Ollama server")
        except Exception as e:
            if isinstance(e, (LLMConnectionError, LLMInvalidRequestError, LLMTimeoutError)):
                raise
            raise LLMConnectionError(f"Unexpected error during streaming: {e}")
    
    async def validate_connection(self) -> bool:
        """
        Validate connection to Ollama.
        
        Returns:
            True if connection is valid, False otherwise
        """
        try:
            # Check if Ollama server is running
            response = await self.client.get(f"{self.base_url}/api/tags")
            if response.status_code != 200:
                return False
            
            # Check if the specific model is available
            data = response.json()
            models = [model["name"] for model in data.get("models", [])]
            return self.model in models
            
        except Exception:
            return False
    
    def _try_parse_tool_calls_from_content(self, content: str) -> List[ToolCall]:
        """
        Try to parse tool calls from content.
        
        This is a basic implementation that looks for JSON-like structures
        in the content that might represent tool calls.
        """
        tool_calls = []
        
        # Look for patterns like {"tool": "function_name", "args": {...}}
        # This is a simplified approach - in practice, you'd need more
        # sophisticated parsing based on your prompt engineering
        
        try:
            # Try to find JSON blocks in the content
            import re
            json_pattern = r'\{[^{}]*\}'
            matches = re.findall(json_pattern, content)
            
            for match in matches:
                try:
                    data = json.loads(match)
                    if "tool" in data and "args" in data:
                        tool_calls.append(ToolCall(
                            id=f"call_{len(tool_calls)}",
                            name=data["tool"],
                            arguments=data["args"]
                        ))
                except json.JSONDecodeError:
                    continue
                    
        except Exception:
            pass
        
        return tool_calls
    
    async def _handle_http_error(self, response: httpx.Response) -> None:
        """Handle HTTP errors from Ollama API."""
        try:
            error_data = response.json()
            error_message = error_data.get("error", "Unknown error")
        except:
            error_message = f"HTTP {response.status_code}: {response.text}"
        
        if response.status_code == 404:
            raise LLMInvalidRequestError(f"Model not found: {error_message}")
        elif response.status_code >= 500:
            raise LLMConnectionError(f"Ollama server error: {error_message}")
        else:
            raise LLMConnectionError(f"Ollama error: {error_message}")
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.client.aclose()
    
    @property
    def name(self) -> str:
        """Provider name."""
        return "ollama"
    
    @property
    def supports_streaming(self) -> bool:
        """Whether provider supports streaming."""
        return True
    
    @property
    def supports_tools(self) -> bool:
        """Whether provider supports function calling."""
        # Limited support - depends on model and prompt engineering
        return False
