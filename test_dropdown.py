#!/usr/bin/env python3
"""
Test script for the dropdown functionality.
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.ui.dropdown import create_tool_confirmation_dropdown, create_simple_confirmation_dropdown

def test_tool_confirmation():
    """Test tool confirmation dropdown."""
    print("Testing tool confirmation dropdown...")
    
    tool_args = {
        "command": "ls -la",
        "working_directory": "/home/<USER>"
    }
    
    result = create_tool_confirmation_dropdown("bash", tool_args)
    print(f"Result: {result}")
    return result

def test_simple_confirmation():
    """Test simple confirmation dropdown."""
    print("Testing simple confirmation dropdown...")
    
    result = create_simple_confirmation_dropdown(
        "Do you want to proceed with this operation?",
        "Confirmation Required"
    )
    print(f"Result: {result}")
    return result

if __name__ == "__main__":
    print("Dropdown Test Script")
    print("=" * 50)
    
    # Test simple confirmation first
    result1 = test_simple_confirmation()
    
    print("\n" + "=" * 50)
    
    # Test tool confirmation
    result2 = test_tool_confirmation()
    
    print(f"\nTest Results:")
    print(f"Simple confirmation: {result1}")
    print(f"Tool confirmation: {result2}")
