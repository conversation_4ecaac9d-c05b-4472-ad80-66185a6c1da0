Metadata-Version: 2.4
Name: arien-ai
Version: 1.0.0
Summary: A sophisticated AI-powered CLI terminal system
Author-email: <PERSON>en AI <<EMAIL>>
License: MIT
Project-URL: Homepage, https://github.com/arien-ai/arien-ai
Project-URL: Documentation, https://docs.arien-ai.com
Project-URL: Repository, https://github.com/arien-ai/arien-ai
Project-URL: Bug Tracker, https://github.com/arien-ai/arien-ai/issues
Keywords: ai,cli,terminal,assistant,automation
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: System :: Shells
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: aiohttp>=3.8.0
Requires-Dist: asyncio-throttle>=1.0.2
Requires-Dist: click>=8.0.0
Requires-Dist: colorama>=0.4.6
Requires-Dist: httpx>=0.24.0
Requires-Dist: pydantic>=2.0.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: rich>=13.0.0
Requires-Dist: typing-extensions>=4.0.0
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "dev"
Requires-Dist: pytest-cov>=4.0.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: isort>=5.12.0; extra == "dev"
Requires-Dist: flake8>=6.0.0; extra == "dev"
Requires-Dist: mypy>=1.0.0; extra == "dev"
Dynamic: license-file

# Arien AI 🤖

A sophisticated AI-powered CLI terminal system built with pure Python. Arien AI provides intelligent assistance through multiple LLM providers, powerful function tools, and advanced execution strategies.

## ✨ Features

### 🧠 Multi-Provider LLM Support
- **Deepseek**: Support for `deepseek-chat` and `deepseek-reasoner` models
- **Ollama**: Local model support with streaming capabilities
- Real-time streaming responses
- Intelligent retry logic with exponential backoff

### 🛠️ Powerful Function Tools
- **Bash Tool**: Secure command execution with real-time output streaming
- **Web Search Tool**: Real-time web information retrieval via DuckDuckGo
- Intelligent parallel vs sequential execution strategies
- Comprehensive safety and security considerations

### 🎨 Advanced User Experience
- Interactive CLI with ball animation and elapsed time display
- Real-time progress indicators and status updates
- User confirmation prompts for destructive operations
- Rich formatting with syntax highlighting
- Comprehensive error handling and recovery

### 🔧 Production-Ready Architecture
- Modular design with clear separation of concerns
- Comprehensive configuration management
- Extensive logging and debugging capabilities
- Cross-platform support (Windows, macOS, Linux)
- Full test suite with high coverage

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- Internet connection for web search and Deepseek API
- (Optional) Ollama installed for local models

### Installation

#### Option 1: Quick Install (Recommended)
```bash
# Download and run the installation script
curl -sSL https://raw.githubusercontent.com/arien-ai/arien-ai/main/install.sh | bash
```

#### Option 2: Manual Installation
```bash
# Clone the repository
git clone https://github.com/arien-ai/arien-ai.git
cd arien-ai

# Install dependencies
pip install -r requirements.txt

# Install in development mode
pip install -e .
```

### Configuration

1. **Set up your API key** (for Deepseek):
```bash
export DEEPSEEK_API_KEY="your-api-key-here"
```

2. **Create a `.env` file** (optional):
```env
DEEPSEEK_API_KEY=your-api-key-here
ARIEN_LLM_PROVIDER=deepseek
ARIEN_LLM_MODEL=deepseek-chat
ARIEN_MAX_TOKENS=4096
ARIEN_TEMPERATURE=0.7
```

### Usage

#### Interactive Mode
```bash
arien
```

#### Single Command Mode
```bash
arien run "List all Python files in the current directory"
arien run "Search for the latest Python 3.12 features"
```

#### Configuration Commands
```bash
# Show current configuration
arien config

# Validate configuration and connections
arien validate

# Show version information
arien version
```

## 📖 Usage Guide

### Available Tools

#### Bash Tool
Execute shell commands safely with comprehensive security features:

```
Examples:
- File operations: "Create a new Python file called hello.py"
- System info: "Show disk usage and memory statistics"
- Git operations: "Check git status and show recent commits"
- Development: "Run pytest on the current project"
```

**Security Features:**
- Command validation and safety checks
- User confirmation for destructive operations
- Timeout handling for long-running commands
- Output limiting to prevent memory issues

#### Web Search Tool
Search the web for real-time information:

```
Examples:
- "What are the latest Python 3.12 features?"
- "Current Bitcoin price in USD"
- "Best practices for Docker deployment 2024"
- "Recent developments in artificial intelligence"
```

**Features:**
- DuckDuckGo integration for privacy
- Result caching to minimize API calls
- Rate limiting and error handling
- Intelligent result formatting

### Interactive Commands

While in interactive mode, you can use these special commands:

- `/help` - Show available commands
- `/config` - Display current configuration
- `/status` - Show system and connection status
- `/clear` - Clear the screen
- `/set key=value` - Update configuration
- `quit` or `exit` - Exit the application

### Configuration Options

#### LLM Settings
```bash
/set model=deepseek-chat          # or deepseek-reasoner
/set temperature=0.7              # 0.0 to 2.0
/set max_tokens=4096              # Maximum response length
```

#### Environment Variables
```env
# LLM Configuration
ARIEN_LLM_PROVIDER=deepseek       # deepseek or ollama
ARIEN_LLM_MODEL=deepseek-chat     # Model name
DEEPSEEK_API_KEY=your-key         # API key for Deepseek
ARIEN_BASE_URL=custom-url         # Custom API endpoint

# Tool Configuration
ARIEN_BASH_ENABLED=true           # Enable/disable bash tool
ARIEN_WEB_SEARCH_ENABLED=true     # Enable/disable web search
ARIEN_BASH_TIMEOUT=300            # Bash command timeout (seconds)

# UI Configuration
ARIEN_SHOW_PROGRESS=true          # Show progress indicators
ARIEN_CONFIRM_DESTRUCTIVE=true    # Confirm dangerous operations
ARIEN_COLOR_OUTPUT=true           # Enable colored output

# System Configuration
ARIEN_LOG_LEVEL=INFO              # DEBUG, INFO, WARNING, ERROR
ARIEN_MAX_RETRIES=3               # Maximum retry attempts
```

## 🏗️ Architecture

### Project Structure
```
src/
├── core/           # Core system components
│   ├── agent.py    # Main AI agent orchestrator
│   ├── exceptions.py # Custom exception classes
│   └── retry.py    # Retry logic with exponential backoff
├── providers/      # LLM provider implementations
│   ├── base.py     # Base provider interface
│   ├── deepseek.py # Deepseek API integration
│   └── ollama.py   # Ollama local model support
├── tools/          # Function tool implementations
│   ├── base.py     # Base tool interface
│   ├── bash_tool.py # Bash command execution
│   └── web_search_tool.py # Web search functionality
├── ui/             # User interface components
│   ├── cli.py      # Main CLI interface
│   ├── animation.py # Progress animations
│   └── formatter.py # Output formatting
├── config/         # Configuration management
│   └── settings.py # Settings and environment handling
└── main.py         # Application entry point
```

### Key Components

#### AI Agent (`src/core/agent.py`)
- Orchestrates LLM providers and function tools
- Implements intelligent execution strategies
- Handles conversation history and context
- Provides comprehensive error handling

#### LLM Providers (`src/providers/`)
- Abstracted interface for multiple LLM providers
- Streaming response support
- Automatic retry logic with exponential backoff
- Comprehensive error classification

#### Function Tools (`src/tools/`)
- Secure and safe tool execution
- Parallel vs sequential execution strategies
- User confirmation for destructive operations
- Detailed execution logging and monitoring

#### User Interface (`src/ui/`)
- Rich CLI with animations and progress indicators
- Real-time streaming response display
- Interactive confirmation prompts
- Comprehensive output formatting

## 🔧 Development

### Setup Development Environment
```bash
# Clone repository
git clone https://github.com/arien-ai/arien-ai.git
cd arien-ai

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install development dependencies
pip install -r requirements.txt -r requirements-dev.txt

# Install in development mode
pip install -e .
```

### Running Tests
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific test categories
pytest -m unit          # Unit tests only
pytest -m integration   # Integration tests only
pytest -m "not slow"    # Skip slow tests
```

### Code Quality
```bash
# Format code
black src/ tests/

# Sort imports
isort src/ tests/

# Lint code
flake8 src/ tests/

# Type checking
mypy src/
```

## 📝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Deepseek](https://deepseek.com/) for providing powerful AI models
- [Ollama](https://ollama.ai/) for local model support
- [Rich](https://github.com/Textualize/rich) for beautiful terminal output
- [Click](https://click.palletsprojects.com/) for CLI framework

## 📞 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/arien-ai/arien-ai/issues)
- 📖 Documentation: [docs.arien-ai.com](https://docs.arien-ai.com)
- 💬 Discussions: [GitHub Discussions](https://github.com/arien-ai/arien-ai/discussions)

---

**Arien AI** - Empowering developers with intelligent CLI assistance 🚀
